{% extends "base.html" %}

{% block title %}Admin Dashboard | ONNYX Platform{% endblock %}

{% block description %}System administration dashboard for ONNYX platform management.{% endblock %}

{% block head %}
<style>
    .admin-card {
        background: linear-gradient(135deg, rgba(255, 0, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
        border: 1px solid rgba(255, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .stat-card {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.2);
        backdrop-filter: blur(10px);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .admin-warning {
        background: linear-gradient(135deg, rgba(255, 165, 0, 0.2) 0%, rgba(255, 0, 0, 0.2) 100%);
        border: 1px solid rgba(255, 165, 0, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Admin Header -->
        <div class="admin-warning p-4 rounded-xl mb-8">
            <div class="flex items-center space-x-3">
                <div class="text-3xl">⚠️</div>
                <div>
                    <h1 class="text-2xl font-orbitron font-bold text-orange-400">System Administrator Panel</h1>
                    <p class="text-orange-300">Welcome, {{ current_user.name }}. You have supreme administrative privileges.</p>
                </div>
            </div>
        </div>

        <!-- System Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card p-6 rounded-xl text-center">
                <div class="text-3xl font-bold text-cyber-cyan">{{ stats.total_identities }}</div>
                <div class="text-sm text-gray-400 mt-1">Total Identities</div>
                <div class="text-xs text-green-400 mt-1">{{ stats.active_identities }} active</div>
            </div>
            
            <div class="stat-card p-6 rounded-xl text-center">
                <div class="text-3xl font-bold text-cyber-purple">{{ stats.total_selas }}</div>
                <div class="text-sm text-gray-400 mt-1">Registered Selas</div>
            </div>
            
            <div class="stat-card p-6 rounded-xl text-center">
                <div class="text-3xl font-bold text-cyber-green">{{ stats.total_pools }}</div>
                <div class="text-sm text-gray-400 mt-1">Jubilee Pools</div>
            </div>
            
            <div class="stat-card p-6 rounded-xl text-center">
                <div class="text-3xl font-bold text-cyber-orange">{{ stats.system_admins }}</div>
                <div class="text-sm text-gray-400 mt-1">System Admins</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="admin-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-red-400 mb-4">🔧 System Management</h3>
                <div class="space-y-3">
                    <a href="{{ url_for('admin.user_management') }}" class="block glass-button-primary p-3 rounded-lg text-center hover:bg-red-600 transition-colors">
                        👥 User Management
                    </a>
                    <a href="{{ url_for('admin.system_management') }}" class="block glass-button-secondary p-3 rounded-lg text-center hover:bg-orange-600 transition-colors">
                        ⚙️ System Management
                    </a>
                    <a href="{{ url_for('auto_mining.dashboard') }}" class="block glass-button-secondary p-3 rounded-lg text-center">
                        ⛏️ Mining Control
                    </a>
                </div>
            </div>
            
            <div class="admin-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-orange-400 mb-4">📊 Platform Overview</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Pending Verification:</span>
                        <span class="text-yellow-400">{{ stats.pending_verification }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total Deeds:</span>
                        <span class="text-green-400">{{ stats.total_deeds }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Available Tokens:</span>
                        <span class="text-blue-400">{{ stats.total_tokens }}</span>
                    </div>
                </div>
            </div>
            
            <div class="admin-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-yellow-400 mb-4">⚡ Quick Actions</h3>
                <div class="space-y-3">
                    <button class="w-full glass-button-primary p-3 rounded-lg" onclick="refreshStats()">
                        🔄 Refresh Statistics
                    </button>
                    <a href="{{ url_for('tokenomics.dashboard') }}" class="block glass-button-secondary p-3 rounded-lg text-center">
                        💰 Tokenomics Dashboard
                    </a>
                    <a href="{{ url_for('dashboard.overview') }}" class="block glass-button-secondary p-3 rounded-lg text-center">
                        🏠 Return to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Identities -->
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">👥 Recent Identities</h3>
                <div class="space-y-3">
                    {% for identity in recent_identities %}
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div>
                            <div class="font-semibold text-white">{{ identity.name or identity[1] }}</div>
                            <div class="text-xs text-gray-400">{{ identity.verification_level or identity[3] }}</div>
                        </div>
                        <div class="text-xs text-gray-500">
                            {{ moment(identity.created_at or identity[2]).fromNow() }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Recent Deeds -->
            <div class="glass-card p-6 rounded-xl">
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">📜 Recent Deeds</h3>
                <div class="space-y-3">
                    {% for deed in recent_deeds %}
                    <div class="p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="font-semibold text-white">{{ deed.deed_type or deed[1] }}</div>
                            <div class="text-xs text-gray-500">
                                {{ moment(deed.timestamp or deed[3]).fromNow() }}
                            </div>
                        </div>
                        <div class="text-sm text-gray-400 mt-1">{{ deed.description or deed[2] }}</div>
                        <div class="text-xs text-gray-500 mt-1">by {{ deed.identity_name or deed[4] }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshStats() {
    fetch('/admin/api/system/stats')
        .then(response => response.json())
        .then(data => {
            // Update statistics display
            console.log('Stats refreshed:', data);
            // TODO: Update DOM elements with new stats
        })
        .catch(error => {
            console.error('Error refreshing stats:', error);
        });
}

// Auto-refresh stats every 30 seconds
setInterval(refreshStats, 30000);
</script>
{% endblock %}
