#!/usr/bin/env python3
"""
Debug Tribes Template Rendering
"""

import requests
import sys

def debug_tribes_template():
    """Debug what's happening with the tribes template"""
    
    print("🔍 Debugging Tribes Template Rendering")
    print("=" * 45)
    
    try:
        response = requests.get('http://127.0.0.1:5000/tribes/overview', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            print(f"✅ Response received: {len(content)} characters")
            
            # Check for template structure
            if 'Biblical Tribes & Nations' in content:
                print("✅ Main title found")
            else:
                print("❌ Main title missing")
            
            # Check for data loops
            if 'Judah' in content:
                print("✅ Judah tribe data found")
            else:
                print("❌ Judah tribe data missing")
            
            if 'Timnah' in content:
                print("✅ Edom duke data found")
            else:
                print("❌ Edom duke data missing")
            
            # Check for enhanced content
            enhanced_checks = [
                ('Genesis 35:10-12', 'Biblical foundation quote'),
                ('glass-card-enhanced', 'Enhanced glass cards'),
                ('showTribalEducation', 'Interactive functions'),
                ('tribal-education-modal', 'Education modal'),
                ('Covenant Privileges', 'Covenant section'),
                ('Numbers 1:44', 'Israel education'),
                ('Nehemiah 7:1-3', 'Gate Keeper section')
            ]
            
            print("\n🔍 Enhanced Content Check:")
            for search_term, description in enhanced_checks:
                if search_term in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description}")
            
            # Look for template errors
            if 'TemplateNotFound' in content:
                print("\n❌ Template not found error")
            elif 'UndefinedError' in content:
                print("\n❌ Template variable undefined error")
            elif 'Traceback' in content:
                print("\n❌ Python traceback in response")
                # Find traceback
                lines = content.split('\n')
                in_traceback = False
                for line in lines:
                    if 'Traceback' in line:
                        in_traceback = True
                    if in_traceback:
                        print(f"   {line}")
                        if line.strip() == '':
                            in_traceback = False
            
            # Check if we're getting the old template
            if 'Enhanced hero section with biblical context' in content:
                print("\n✅ Enhanced template is being used")
            else:
                print("\n❌ Old template is being used or template not updated")
            
            # Sample a small section to see what we're getting
            print(f"\n📄 Content sample (first 500 chars):")
            print(content[:500])
            print("...")
            
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error debugging template: {e}")
        return False

if __name__ == "__main__":
    debug_tribes_template()
