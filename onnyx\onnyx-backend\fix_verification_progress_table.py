#!/usr/bin/env python3
"""
Fix Verification Progress Table Missing Error
"""

import sqlite3
import sys
import os

def create_verification_progress_table():
    """Create the missing verification_progress table"""
    
    print("🔧 Creating verification_progress table...")
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='verification_progress'")
        if cursor.fetchone():
            print("ℹ️ verification_progress table already exists")
            conn.close()
            return True
        
        # Create verification_progress table
        cursor.execute('''
            CREATE TABLE verification_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                verification_type TEXT NOT NULL,
                step_name TEXT NOT NULL,
                step_status TEXT DEFAULT 'pending',
                step_data TEXT DEFAULT '{}',
                completed_at INTEGER,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                UNIQUE(identity_id, verification_type, step_name)
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX idx_verification_progress_identity ON verification_progress(identity_id)')
        cursor.execute('CREATE INDEX idx_verification_progress_type ON verification_progress(verification_type)')
        cursor.execute('CREATE INDEX idx_verification_progress_status ON verification_progress(step_status)')
        
        conn.commit()
        print("✅ Created verification_progress table successfully")
        
        # Verify table was created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='verification_progress'")
        if cursor.fetchone():
            print("✅ verification_progress table verified")
        else:
            print("❌ verification_progress table not found after creation")
            conn.close()
            return False
        
        # Check table structure
        cursor.execute('PRAGMA table_info(verification_progress)')
        columns = cursor.fetchall()
        print(f"✅ Table has {len(columns)} columns:")
        for col in columns:
            print(f"   - {col[1]} {col[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating verification_progress table: {e}")
        return False

def test_verification_progress_access():
    """Test accessing the verification_progress table"""
    
    print("\n🧪 Testing verification_progress table access...")
    
    try:
        # Test the query that was failing
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test the exact query from the error
        test_identity_id = 'ONX4d0fb6bb0dda45a7'
        cursor.execute("SELECT * FROM verification_progress WHERE identity_id = ?", (test_identity_id,))
        results = cursor.fetchall()
        
        print(f"✅ Query executed successfully")
        print(f"   Found {len(results)} verification progress records for genesis admin")
        
        # If no records exist, create a sample record for the genesis admin
        if len(results) == 0:
            print("   Creating initial verification progress record...")
            import time
            current_time = int(time.time())
            
            cursor.execute('''
                INSERT INTO verification_progress 
                (identity_id, verification_type, step_name, step_status, step_data, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_identity_id,
                'covenant_identity',
                'initial_setup',
                'completed',
                '{"admin_bypass": true, "genesis_identity": true}',
                current_time,
                current_time
            ))
            
            conn.commit()
            print("✅ Created initial verification progress record")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing verification_progress access: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing Verification Progress Table Issue")
    print("=" * 50)
    
    # Create the table
    table_created = create_verification_progress_table()
    
    # Test access
    access_ok = test_verification_progress_access()
    
    print("\n📊 Results:")
    print("=" * 20)
    
    if table_created:
        print("✅ Table creation: SUCCESS")
    else:
        print("❌ Table creation: FAILED")
    
    if access_ok:
        print("✅ Table access: SUCCESS")
    else:
        print("❌ Table access: FAILED")
    
    overall_success = table_created and access_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: verification_progress table is now working!")
        print("   - Table created with proper structure")
        print("   - Indexes added for performance")
        print("   - Initial record created for genesis admin")
        print("   - Covenant Identity dashboard should now work")
    else:
        print("\n⚠️ Some issues remain - check details above")
    
    sys.exit(0 if overall_success else 1)
