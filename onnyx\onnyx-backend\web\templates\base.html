<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <title>{% block title %}ONNYX Platform - Reclaim Your Digital Identity{% endblock %}</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="{% block description %}Reclaim your digital identity with ONNYX - the most secure blockchain platform for verified business operations. Own your data, control your commerce, build your reputation.{% endblock %}">
    <meta name="keywords" content="blockchain, digital identity, business verification, cryptocurrency, decentralized, biblical tokenomics, validator network, secure commerce">
    <meta name="author" content="ONNYX Platform">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ request.url }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title() }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ self.description() }}{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:image" content="{{ url_for('static', filename='images/onnyx_og_image.png', _external=True) }}">
    <meta property="og:site_name" content="ONNYX Platform">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}{{ self.title() }}{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}{{ self.description() }}{% endblock %}">
    <meta name="twitter:image" content="{{ url_for('static', filename='images/onnyx_twitter_card.png', _external=True) }}">
    <meta name="twitter:site" content="@ONNYXPlatform">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "ONNYX Platform",
        "description": "Blockchain platform for verified business operations and digital identity management",
        "url": "{{ request.url_root }}",
        "logo": "{{ url_for('static', filename='images/onnyx_logo.png', _external=True) }}",
        "sameAs": [
            "https://twitter.com/ONNYXPlatform",
            "https://linkedin.com/company/onnyx-platform"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "email": "<EMAIL>"
        }
    }
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="48x48" href="{{ url_for('static', filename='images/favicon-48x48.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <meta name="theme-color" content="#00fff7">

    <!-- Simple Google Fonts Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS with custom config -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'orbitron': ['Orbitron', 'monospace'],
                        'montserrat': ['Montserrat', 'sans-serif'],
                    },
                    colors: {
                        'onyx': {
                            'black': '#1a1a1a',
                            'gray': '#2a2a2a',
                            'light': '#3a3a3a',
                        },
                        'cyber': {
                            'cyan': '#00fff7',
                            'purple': '#9a00ff',
                            'blue': '#0066ff',
                        },
                        'text': {
                            'primary': '#eeeeee',
                            'secondary': '#d1d5db',
                            'tertiary': '#9ca3af',
                            'muted': '#6b7280',
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}?v=compact-fix-2024">

    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- QR Code library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Alpine.js for interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    {% block head %}{% endblock %}
</head>
<body class="bg-onyx-black text-text-primary font-montserrat overflow-x-hidden"
      style="min-height: 100vh; display: flex; flex-direction: column; margin: 0; padding: 0;">
    <!-- Professional Navigation Bar -->
    <nav class="onnyx-navbar">
        <div class="navbar-container">
            <!-- Enhanced Logo Section with Real Logo Integration -->
            <div class="navbar-logo">
                <a href="{{ url_for('index') }}" class="logo-link" id="main-logo-link">
                    <div class="logo-icon" id="logo-icon-container" style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center;">
                        <!-- Primary: Use actual logo image -->
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             id="logo-image"
                             class="onnyx-nav-logo w-12 h-12 md:w-16 md:h-16 object-contain"
                             onerror="console.log('Header logo failed to load:', this.src); this.style.display='none'; document.getElementById('logo-fallback').style.display='block';"
                             onload="console.log('Header logo loaded successfully:', this.src);">
                        <!-- Fallback: Cyberpunk symbol if logo fails to load -->
                        <span class="logo-symbol" id="logo-fallback" style="display: none; color: #00d4ff; font-size: 24px;">⬢</span>
                    </div>
                    <span class="logo-text">ONNYX</span>
                </a>
            </div>

            <!-- Main Navigation Links -->
            <div class="navbar-nav">
                <a href="{{ url_for('index') }}" class="nav-item" data-route="index">
                    <span class="nav-icon">🏠</span>
                    <span class="nav-label">Home</span>
                </a>
                <a href="{{ url_for('sela.directory') }}" class="nav-item" data-route="sela">
                    <span class="nav-icon">🏢</span>
                    <span class="nav-label">Validators</span>
                </a>
                <a href="{{ url_for('explorer.index') }}" class="nav-item" data-route="explorer">
                    <span class="nav-icon">🔍</span>
                    <span class="nav-label">Explorer</span>
                </a>
                <a href="{{ url_for('tokenomics.overview') }}" class="nav-item nav-item-featured" data-route="tokenomics">
                    <span class="nav-icon">📜</span>
                    <span class="nav-label">Biblical Tokenomics</span>
                    <span class="nav-badge">New</span>
                </a>
                {% if current_user %}
                <a href="{{ url_for('dashboard.overview') }}" class="nav-item" data-route="dashboard">
                    <span class="nav-icon">📊</span>
                    <span class="nav-label">Dashboard</span>
                </a>
                <a href="{{ url_for('auto_mining.dashboard') }}" class="nav-item" data-route="auto_mining">
                    <span class="nav-icon">⚡</span>
                    <span class="nav-label">Auto-Mining</span>
                </a>
                {% endif %}
            </div>

            <!-- Mobile Menu Toggle -->
            <div class="mobile-menu-toggle">
                <button class="mobile-menu-btn" id="mobile-menu-button" aria-label="Toggle mobile menu" aria-expanded="false">
                    <div class="hamburger" id="hamburger-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>

                <!-- Mobile Menu Overlay -->
                <div class="mobile-menu-overlay" id="mobile-menu-overlay" style="display: none;" aria-hidden="true">
                    <div class="mobile-menu-content">
                        <!-- Enhanced Navigation Section with Active State Detection -->
                        <div class="mobile-nav-section">
                            <div class="mobile-nav-title">Navigation</div>
                            <a href="{{ url_for('index') }}"
                               class="mobile-nav-item {{ 'active' if request.endpoint == 'index' else '' }}"
                               data-route="index"
                               aria-label="Home page">
                                <span class="mobile-nav-icon" aria-hidden="true">🏠</span>
                                <span>Home</span>
                            </a>
                            <a href="{{ url_for('sela.directory') }}"
                               class="mobile-nav-item {{ 'active' if request.endpoint and 'sela' in request.endpoint else '' }}"
                               data-route="sela"
                               aria-label="Validator network directory">
                                <span class="mobile-nav-icon" aria-hidden="true">🏢</span>
                                <span>Validators</span>
                            </a>
                            <a href="{{ url_for('explorer.index') }}"
                               class="mobile-nav-item {{ 'active' if request.endpoint and 'explorer' in request.endpoint else '' }}"
                               data-route="explorer"
                               aria-label="Blockchain explorer">
                                <span class="mobile-nav-icon" aria-hidden="true">🔍</span>
                                <span>Explorer</span>
                            </a>
                            <a href="{{ url_for('tokenomics.overview') }}"
                               class="mobile-nav-item featured {{ 'active' if request.endpoint and 'tokenomics' in request.endpoint else '' }}"
                               data-route="tokenomics"
                               aria-label="Biblical tokenomics system">
                                <span class="mobile-nav-icon" aria-hidden="true">📜</span>
                                <span>Biblical Tokenomics</span>
                            </a>
                            {% if current_user %}
                            <a href="{{ url_for('dashboard.overview') }}"
                               class="mobile-nav-item {{ 'active' if request.endpoint and 'dashboard' in request.endpoint else '' }}"
                               data-route="dashboard"
                               aria-label="User dashboard">
                                <span class="mobile-nav-icon" aria-hidden="true">📊</span>
                                <span>Dashboard</span>
                            </a>
                            <a href="{{ url_for('auto_mining.dashboard') }}"
                               class="mobile-nav-item {{ 'active' if request.endpoint and 'auto_mining' in request.endpoint else '' }}"
                               data-route="auto_mining"
                               aria-label="Auto-mining dashboard">
                                <span class="mobile-nav-icon" aria-hidden="true">⚡</span>
                                <span>Auto-Mining</span>
                            </a>
                            {% endif %}
                        </div>

                        <!-- Authentication Section -->
                        {% if current_user %}
                        <div class="mobile-nav-section">
                            <div class="mobile-nav-title">Account</div>
                            <a href="{{ url_for('tokenomics.dashboard') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">📜</span>
                                <span>Biblical Tokenomics</span>
                            </a>
                            <a href="{{ url_for('dashboard.profile') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">⚙️</span>
                                <span>Profile Settings</span>
                            </a>
                            <a href="{{ url_for('auth.identity_dashboard') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">🛡️</span>
                                <span>Covenant Identity</span>
                            </a>
                            <a href="{{ url_for('dashboard.identity_details') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">👤</span>
                                <span>My Identity</span>
                            </a>
                            <a href="{{ url_for('auth.logout') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">🚪</span>
                                <span>Logout</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Authentication Section -->
            <div class="navbar-auth">
                {% if current_user %}
                    <!-- User Profile Dropdown -->
                    <div class="user-dropdown">
                        <button class="user-button">
                            <div class="user-avatar">
                                <span class="user-initial">{{ current_user.name[0].upper() }}</span>
                            </div>
                            <div class="user-details">
                                <span class="user-name">{{ current_user.name }}</span>
                                <span class="user-status">Online</span>
                            </div>
                            <svg class="dropdown-chevron" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="user-menu" style="display: none;">
                            <div class="menu-header">
                                <div class="menu-user-info">
                                    <div class="menu-avatar">
                                        <span>{{ current_user.name[0].upper() }}</span>
                                    </div>
                                    <div>
                                        <div class="menu-name">{{ current_user.name }}</div>
                                        <div class="menu-email">{{ current_user.email or 'No email' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="menu-divider"></div>
                            <a href="{{ url_for('dashboard.overview') }}" class="menu-item">
                                <span class="menu-icon">📊</span>
                                <span>Dashboard</span>
                            </a>
                            <a href="{{ url_for('tokenomics.dashboard') }}" class="menu-item">
                                <span class="menu-icon">📜</span>
                                <span>Biblical Tokenomics</span>
                            </a>
                            <a href="{{ url_for('dashboard.profile') }}" class="menu-item">
                                <span class="menu-icon">⚙️</span>
                                <span>Profile Settings</span>
                            </a>
                            <a href="{{ url_for('auth.identity_dashboard') }}" class="menu-item">
                                <span class="menu-icon">🛡️</span>
                                <span>Covenant Identity</span>
                            </a>
                            <a href="{{ url_for('dashboard.identity_details') }}" class="menu-item">
                                <span class="menu-icon">👤</span>
                                <span>My Identity</span>
                            </a>
                            <a href="{{ url_for('onboarding.dashboard') }}" class="menu-item">
                                <span class="menu-icon">🛡️</span>
                                <span>Manual Onboarding</span>
                            </a>
                            <div class="menu-divider"></div>
                            <a href="{{ url_for('auth.logout') }}" class="menu-item menu-item-danger">
                                <span class="menu-icon">🚪</span>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Floating Authentication Orb - Only for non-authenticated users -->
    {% if not current_user %}
    <div class="floating-auth-orb" id="floating-auth-orb">
        <div class="orb-core">
            <div class="orb-pulse"></div>
            <div class="orb-icon">🔐</div>
        </div>

        <!-- Floating Menu -->
        <div class="orb-menu" id="orb-menu">
            <div class="orb-menu-content">
                <div class="orb-menu-header">
                    <div class="orb-menu-title">Access ONNYX</div>
                    <div class="orb-menu-subtitle">Secure Authentication</div>
                </div>

                <div class="orb-menu-options">
                    <a href="{{ url_for('auth.login') }}" class="orb-option orb-option-login">
                        <div class="orb-option-icon">🔑</div>
                        <div class="orb-option-content">
                            <div class="orb-option-title">Access Portal</div>
                            <div class="orb-option-desc">Sign in to your account</div>
                        </div>
                    </a>

                    <a href="{{ url_for('register_choice') }}" class="orb-option orb-option-register">
                        <div class="orb-option-icon">✨</div>
                        <div class="orb-option-content">
                            <div class="orb-option-title">Verify Identity</div>
                            <div class="orb-option-desc">Create new account</div>
                        </div>
                    </a>
                </div>

                <div class="orb-menu-footer">
                    <div class="orb-security-badge">
                        <span class="security-icon">🛡️</span>
                        <span>Quantum-Secured</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Main Content Wrapper - Flex container that expands to push footer down -->
    <div style="flex: 1 0 auto; display: flex; flex-direction: column; min-height: calc(100vh - 64px);">
        <!-- Content area with top padding for fixed nav -->
        <main style="flex: 1 0 auto; padding-top: 4rem; padding-bottom: 4rem; width: 100%; position: relative;">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                    {% for category, message in messages %}
                    <div class="glass-card mb-4 p-4 rounded-xl border {% if category == 'error' %}border-red-500/30 bg-red-500/10 text-red-300{% elif category == 'success' %}border-green-500/30 bg-green-500/10 text-green-300{% elif category == 'warning' %}border-yellow-500/30 bg-yellow-500/10 text-yellow-300{% else %}border-cyber-cyan/30 bg-cyber-cyan/10 text-cyber-cyan{% endif %} backdrop-blur-sm">
                        <div class="flex items-center space-x-3">
                            {% if category == 'error' %}
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'success' %}
                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'warning' %}
                            <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            {% else %}
                            <svg class="w-5 h-5 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% endif %}
                            <span class="font-medium">{{ message }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endwith %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Precision Footer - Enhanced UX/UI with Perfect Symmetry -->
    <footer class="footer-responsive">
        <div class="footer-grid">
            <!-- ONNYX Platform Navigation -->
            <div class="footer-section">
                <h3 class="footer-section-title">⬢ Platform</h3>
                <ul class="footer-content-list">
                    <li><a href="{{ url_for('index') }}" class="footer-link">
                        <span class="footer-icon">🏠</span>
                        <span>Home</span>
                    </a></li>
                    <li><a href="{{ url_for('dashboard.overview') }}" class="footer-link">
                        <span class="footer-icon">📊</span>
                        <span>Dashboard</span>
                    </a></li>
                    <li><a href="{{ url_for('tokenomics.overview') }}" class="footer-link">
                        <span class="footer-icon">📜</span>
                        <span>Tokenomics</span>
                    </a></li>
                </ul>
            </div>

            <!-- Network Services -->
            <div class="footer-section">
                <h3 class="footer-section-title">🚀 Network</h3>
                <ul class="footer-content-list">
                    <li><a href="{{ url_for('sela.directory') }}" class="footer-link">
                        <span class="footer-icon">🏢</span>
                        <span>Validators</span>
                    </a></li>
                    <li><a href="{{ url_for('explorer.index') }}" class="footer-link">
                        <span class="footer-icon">🔍</span>
                        <span>Explorer</span>
                    </a></li>
                    <li><a href="{{ url_for('eden_mode.step1') }}" class="footer-link">
                        <span class="footer-icon">🌟</span>
                        <span>Eden Mode</span>
                    </a></li>
                </ul>
            </div>

            <!-- Live Network Stats -->
            <div class="footer-section">
                <h3 class="footer-section-title">📊 Stats</h3>
                <ul class="footer-content-list">
                    <li class="footer-stat-item">
                        <span class="stat-label">Identities</span>
                        <span class="stat-value text-cyber-cyan">{{ platform_stats.identities }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Validators</span>
                        <span class="stat-value text-cyber-purple">{{ platform_stats.selas }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Blocks</span>
                        <span class="stat-value text-cyber-green">{{ platform_stats.blocks }}</span>
                    </li>
                </ul>
            </div>

            <!-- Core Technology -->
            <div class="footer-section">
                <h3 class="footer-section-title">⚙️ Tech</h3>
                <ul class="footer-content-list">
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-cyan"></div>
                        <span class="footer-tech-label">Quantum Security</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-purple"></div>
                        <span class="footer-tech-label">Trust Protocol</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-blue"></div>
                        <span class="footer-tech-label">Token Economy</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p>&copy; 2024 ONNYX Platform. Securing the future of digital commerce.</p>
                <div class="flex items-center space-x-6 text-sm">
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>Network Online</span>
                    </span>
                    <span class="font-mono">v1.0.0</span>
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 {% if blockchain_status.network_running %}bg-cyber-cyan{% else %}bg-gray-400{% endif %} rounded-full {% if blockchain_status.network_running %}animate-pulse{% endif %}"></div>
                        <span>{% if blockchain_status.network_running %}Blockchain Active{% else %}Blockchain Starting{% endif %}</span>
                    </span>
                    {% if blockchain_status.mining_enabled %}
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                        <span>Mining Active</span>
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </footer>

    <!-- Enhanced Interface System - Advanced Cyberpunk Interactions -->
    <script src="{{ url_for('static', filename='js/enhanced-interface.js') }}"></script>

    <!-- Consistent Hover Effects System - Standardized Card Interactions -->
    <script src="{{ url_for('static', filename='js/consistent-hover-effects.js') }}"></script>

    <!-- Enhanced Navigation System - Production Ready -->
    <script src="{{ url_for('static', filename='js/enhanced-navigation.js') }}"></script>

    <!-- Enhanced Scroll Effects -->
    <script src="{{ url_for('static', filename='js/scroll-effects.js') }}"></script>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Production Validation Suite -->
    <script src="{{ url_for('static', filename='js/production-validation.js') }}"></script>

    <!-- Platform Audit Validator -->
    <script src="{{ url_for('static', filename='js/platform-audit-validator.js') }}"></script>

    <!-- Performance Optimizer -->
    <script src="{{ url_for('static', filename='js/performance-optimizer.js') }}"></script>

    <!-- Comprehensive Platform Test -->
    <script src="{{ url_for('static', filename='js/comprehensive-platform-test.js') }}"></script>
    <!-- Mobile Test Suite (Development Only) -->
    {% if config.DEBUG %}
    <script src="{{ url_for('static', filename='js/mobile-test-suite.js') }}"></script>
    {% endif %}

    <!-- Navigation Testing Script (Development Only) -->
    {% if config.DEBUG %}
    <script src="{{ url_for('static', filename='js/navigation-test.js') }}"></script>
    <script src="{{ url_for('static', filename='js/navigation-verification.js') }}"></script>
    <script src="{{ url_for('static', filename='js/navigation-fix-verification.js') }}"></script>
    <script src="{{ url_for('static', filename='js/navigation-comprehensive-test.js') }}"></script>
    {% endif %}

    <!-- ONNYX Navigation System -->
    <script src="{{ url_for('static', filename='js/onnyx-navigation.js') }}"></script>

    <!-- Blockchain Data Formatter -->
    <script src="{{ url_for('static', filename='js/blockchain-data-formatter.js') }}"></script>

    <!-- Orb Menu Enhancement -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile Menu Enhancement
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        const hamburgerIcon = document.getElementById('hamburger-icon');
        
        if (mobileMenuButton && mobileMenuOverlay) {
            let isMobileMenuOpen = false;
            
            function toggleMobileMenu() {
                isMobileMenuOpen = !isMobileMenuOpen;
                
                if (isMobileMenuOpen) {
                    // Show mobile menu
                    mobileMenuOverlay.style.display = 'block';
                    mobileMenuOverlay.setAttribute('aria-hidden', 'false');
                    mobileMenuButton.setAttribute('aria-expanded', 'true');
                    
                    // Add active class to hamburger
                    if (hamburgerIcon) {
                        hamburgerIcon.classList.add('active');
                    }
                    
                    // Animate in
                    setTimeout(() => {
                        mobileMenuOverlay.classList.add('opening');
                    }, 10);
                    
                    // Prevent body scroll
                    document.body.style.overflow = 'hidden';
                    
                    console.log('Mobile menu opened');
                } else {
                    // Hide mobile menu
                    mobileMenuOverlay.classList.remove('opening');
                    mobileMenuOverlay.classList.add('closing');
                    mobileMenuOverlay.setAttribute('aria-hidden', 'true');
                    mobileMenuButton.setAttribute('aria-expanded', 'false');
                    
                    // Remove active class from hamburger
                    if (hamburgerIcon) {
                        hamburgerIcon.classList.remove('active');
                    }
                    
                    // Hide after animation
                    setTimeout(() => {
                        mobileMenuOverlay.style.display = 'none';
                        mobileMenuOverlay.classList.remove('closing');
                    }, 300);
                    
                    // Restore body scroll
                    document.body.style.overflow = '';
                    
                    console.log('Mobile menu closed');
                }
            }
            
            // Mobile menu button click
            mobileMenuButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleMobileMenu();
            });
            
            // Close when clicking outside
            mobileMenuOverlay.addEventListener('click', function(e) {
                if (e.target === mobileMenuOverlay) {
                    toggleMobileMenu();
                }
            });
            
            // Close when clicking nav items
            const mobileNavItems = mobileMenuOverlay.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    setTimeout(() => {
                        if (isMobileMenuOpen) {
                            toggleMobileMenu();
                        }
                    }, 150);
                });
            });
            
            console.log('Mobile menu initialized');
        }

        // Mobile Touch Enhancements
        function addMobileTouchEnhancements() {
            // Add touch feedback to interactive elements
            const touchElements = document.querySelectorAll(
                '.nav-item, .auth-btn, .user-button, .mobile-menu-btn, .orb-core, .orb-option, .menu-item, .mobile-nav-item'
            );
            
            touchElements.forEach(element => {
                // Add touch start feedback
                element.addEventListener('touchstart', function(e) {
                    this.style.transform = 'scale(0.95)';
                    this.style.transition = 'transform 0.1s ease';
                }, { passive: true });
                
                // Remove touch feedback
                element.addEventListener('touchend', function(e) {
                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.transition = '';
                    }, 100);
                }, { passive: true });
                
                // Handle touch cancel
                element.addEventListener('touchcancel', function(e) {
                    this.style.transform = '';
                    this.style.transition = '';
                }, { passive: true });
            });
            
            // Prevent zoom on double tap for specific elements
            const noZoomElements = document.querySelectorAll('.nav-item, .user-button, .mobile-menu-btn');
            noZoomElements.forEach(element => {
                element.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    setTimeout(() => {
                        this.click();
                    }, 10);
                }, { passive: false });
            });
            
            console.log('Mobile touch enhancements added');
        }
        
        // Apply mobile enhancements if on mobile device
        if (window.innerWidth <= 768 || 'ontouchstart' in window) {
            addMobileTouchEnhancements();
        }
        
        // Reapply on orientation change
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                if (window.innerWidth <= 768) {
                    addMobileTouchEnhancements();
                }
            }, 300);
        });

        // User Dropdown Menu Enhancement
        const userDropdown = document.querySelector('.user-dropdown');
        const userButton = document.querySelector('.user-button');
        const userMenu = document.querySelector('.user-menu');
        
        if (userDropdown && userButton && userMenu) {
            let isMenuOpen = false;
            
            // Function to position menu within viewport
            function positionMenu() {
                if (!userMenu || !userButton) return;
                
                const buttonRect = userButton.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                
                // Reset positioning styles
                userMenu.style.right = '';
                userMenu.style.left = '';
                userMenu.style.top = '';
                userMenu.style.bottom = '';
                userMenu.style.transform = '';
                
                // For mobile, use full width dropdown
                if (viewportWidth <= 768) {
                    userMenu.style.right = '1rem';
                    userMenu.style.left = '1rem';
                    userMenu.style.width = 'auto';
                    userMenu.style.maxWidth = 'none';
                    userMenu.style.top = 'calc(100% + 0.75rem)';
                } else {
                    // Desktop positioning
                    userMenu.style.right = '0';
                    userMenu.style.top = 'calc(100% + 0.75rem)';
                    userMenu.style.width = '300px';
                    
                    // Force a reflow to get accurate measurements
                    userMenu.offsetHeight;
                    
                    const menuRect = userMenu.getBoundingClientRect();
                    
                    // Check horizontal overflow
                    if (menuRect.left < 16) { 
                        const adjustment = 16 - menuRect.left;
                        userMenu.style.transform = `translateX(${adjustment}px)`;
                    } else if (menuRect.right > viewportWidth - 16) { 
                        const adjustment = menuRect.right - (viewportWidth - 16);
                        userMenu.style.transform = `translateX(-${adjustment}px)`;
                    }
                }
                
                // Check vertical overflow
                const menuRect = userMenu.getBoundingClientRect();
                if (menuRect.bottom > viewportHeight - 16) {
                    userMenu.style.top = 'auto';
                    userMenu.style.bottom = 'calc(100% + 0.75rem)';
                }
                
                console.log('Menu positioned for viewport:', viewportWidth + 'x' + viewportHeight);
            }
            
            // Toggle menu visibility
            function toggleMenu() {
                isMenuOpen = !isMenuOpen;
                
                if (isMenuOpen) {
                    userMenu.style.visibility = 'visible';
                    userMenu.style.pointerEvents = 'all';
                    positionMenu();
                    
                    setTimeout(() => {
                        userMenu.classList.add('menu-visible');
                    }, 10);
                    
                    console.log('User menu opened');
                } else {
                    userMenu.classList.remove('menu-visible');
                    
                    setTimeout(() => {
                        userMenu.style.visibility = 'hidden';
                        userMenu.style.pointerEvents = 'none';
                    }, 300);
                    
                    console.log('User menu closed');
                }
            }
            
            // Click handler for user button
            userButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleMenu();
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target) && isMenuOpen) {
                    toggleMenu();
                }
            });
            
            // Reposition on window resize and orientation change
            window.addEventListener('resize', function() {
                if (isMenuOpen) {
                    positionMenu();
                }
            });
            
            window.addEventListener('orientationchange', function() {
                setTimeout(() => {
                    if (isMenuOpen) {
                        positionMenu();
                    }
                }, 100);
            });
            
            console.log('User dropdown menu initialized');
        }

        // Floating Authentication Orb (existing code)
        const orbContainer = document.getElementById('floating-auth-orb');
        const orbMenu = document.getElementById('orb-menu');

        if (orbContainer && orbMenu) {
            let menuTimeout;
            let isMenuVisible = false;

            // Ensure orb is always visible and properly positioned
            function ensureOrbVisibility() {
                if (orbContainer) {
                    // Force visibility
                    orbContainer.style.display = 'block';
                    orbContainer.style.visibility = 'visible';
                    orbContainer.style.opacity = '1';
                    
                    // Simple positioning that works at all zoom levels
                    orbContainer.style.position = 'fixed';
                    orbContainer.style.top = '96px'; // 64px nav + 32px spacing
                    orbContainer.style.right = '32px'; // 32px from right edge
                    orbContainer.style.zIndex = '999';
                    
                    console.log('Orb visibility ensured:', {
                        display: orbContainer.style.display,
                        visibility: orbContainer.style.visibility,
                        position: orbContainer.getBoundingClientRect()
                    });
                }
            }

            // Call immediately and on resize/zoom
            ensureOrbVisibility();
            window.addEventListener('resize', ensureOrbVisibility);
            window.addEventListener('orientationchange', ensureOrbVisibility);

            function showMenu() {
                clearTimeout(menuTimeout);
                isMenuVisible = true;
                
                // Immediately make menu interactive
                orbMenu.style.opacity = '1';
                orbMenu.style.visibility = 'visible';
                orbMenu.style.transform = 'translateY(0) scale(1)';
                orbMenu.style.pointerEvents = 'all';
                orbMenu.style.zIndex = '1002';
                
                orbMenu.classList.add('menu-visible');
                orbContainer.style.zIndex = '1001';
                
                console.log('Menu shown and made interactive');
            }

            function hideMenu() {
                menuTimeout = setTimeout(() => {
                    isMenuVisible = false;
                    
                    orbMenu.style.opacity = '0';
                    orbMenu.style.visibility = 'hidden';
                    orbMenu.style.transform = 'translateY(-8px) scale(0.95)';
                    orbMenu.style.pointerEvents = 'none';
                    
                    orbMenu.classList.remove('menu-visible');
                    orbContainer.style.zIndex = '999';
                    
                    console.log('Menu hidden');
                }, 200); // Reduced delay
            }

            // Show menu on orb hover
            orbContainer.addEventListener('mouseenter', function() {
                console.log('Orb mouseenter');
                showMenu();
            });

            // Keep menu visible when hovering over menu itself
            orbMenu.addEventListener('mouseenter', function() {
                console.log('Menu mouseenter');
                showMenu();
            });

            // Hide menu when leaving orb (with delay)
            orbContainer.addEventListener('mouseleave', function() {
                console.log('Orb mouseleave');
                hideMenu();
            });

            // Hide menu when leaving menu (with delay)
            orbMenu.addEventListener('mouseleave', function() {
                console.log('Menu mouseleave');
                hideMenu();
            });

            // Click to toggle menu (for mobile/touch)
            orbContainer.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Orb clicked, menu visible:', isMenuVisible);
                
                if (isMenuVisible) {
                    clearTimeout(menuTimeout);
                    hideMenu();
                } else {
                    showMenu();
                }
            });

            // Ensure menu links are clickable with enhanced event handling
            const menuLinks = orbMenu.querySelectorAll('a');
            menuLinks.forEach(link => {
                // Remove any existing event listeners by cloning
                const newLink = link.cloneNode(true);
                link.parentNode.replaceChild(newLink, link);
                
                newLink.addEventListener('click', function(e) {
                    console.log('Menu link clicked:', this.href);
                    // Allow normal navigation
                    window.location.href = this.href;
                });
                
                newLink.addEventListener('mousedown', function(e) {
                    console.log('Menu link mousedown:', this.href);
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!orbContainer.contains(e.target) && !orbMenu.contains(e.target)) {
                    console.log('Clicked outside, hiding menu');
                    clearTimeout(menuTimeout);
                    hideMenu();
                }
            });

            // Debug: Log orb position every 5 seconds
            setInterval(() => {
                if (orbContainer) {
                    const rect = orbContainer.getBoundingClientRect();
                    console.log('Orb position check:', {
                        visible: rect.width > 0 && rect.height > 0,
                        position: {
                            top: rect.top,
                            right: window.innerWidth - rect.right,
                            width: rect.width,
                            height: rect.height
                        },
                        styles: {
                            display: window.getComputedStyle(orbContainer).display,
                            visibility: window.getComputedStyle(orbContainer).visibility,
                            opacity: window.getComputedStyle(orbContainer).opacity
                        }
                    });
                }
            }, 5000);

        } else {
            console.error('Orb container or menu not found:', {
                orbContainer: !!orbContainer,
                orbMenu: !!orbMenu
            });
        }
    });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
