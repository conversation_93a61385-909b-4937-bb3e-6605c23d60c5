{% extends "base.html" %}

{% block title %}Biblical Tribes Overview - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Enhanced Hero Section with Biblical Context -->
    <div class="py-20 relative overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-20 left-10 w-32 h-32 bg-cyber-cyan/10 rounded-full blur-xl animate-pulse"></div>
            <div class="absolute bottom-20 right-10 w-40 h-40 bg-cyber-purple/10 rounded-full blur-xl animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-cyber-blue/5 rounded-full blur-2xl animate-pulse" style="animation-delay: 2s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Biblical Tribes & Nations
                    </span>
                </h1>
                <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed mb-4">
                    "And he said unto me, Thou art my servant, O Israel, in whom I will be glorified" - Isaiah 49:3 (KJV)
                </p>
                <div class="text-lg text-text-tertiary max-w-4xl mx-auto mb-8">
                    Discover your covenant lineage through the comprehensive biblical genealogies recorded in Scripture.
                    From the 12 Tribes of Israel to the witness nations of Edom, Ishmael, and the sons of Noah.
                </div>

                <!-- Biblical Foundation Quote -->
                <div class="glass-card-enhanced p-6 max-w-4xl mx-auto mb-8">
                    <div class="text-cyber-cyan text-sm font-orbitron mb-2">Genesis 35:10-12 (KJV)</div>
                    <blockquote class="text-lg text-text-primary italic leading-relaxed mb-4">
                        "And God said unto him, Thy name is Jacob: thy name shall not be called any more Jacob,
                        but Israel shall be thy name: and he called his name Israel. And God said unto him,
                        I am God Almighty: be fruitful and multiply; a nation and a company of nations shall be of thee,
                        and kings shall come out of thy loins; And the land which I gave Abraham and Isaac, to thee I will give it,
                        and to thy seed after thee will I give the land."
                    </blockquote>
                    <div class="text-center text-sm text-text-tertiary">
                        The eternal covenant establishing the tribes of Israel and witness nations
                    </div>
                </div>

                <!-- Quick Navigation -->
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="#israel-tribes" class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        🏛️ 12 Tribes of Israel
                    </a>
                    <a href="#edom-dukes" class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105" style="border: 2px solid var(--cyber-purple);">
                        👑 12 Dukes of Edom
                    </a>
                    <a href="#ishmael-princes" class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105" style="border: 2px solid var(--cyber-blue);">
                        🏜️ 12 Princes of Ishmael
                    </a>
                    <a href="#gate-keeper-council" class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105" style="border: 2px solid var(--cyber-green);">
                        ⚖️ View Gate Keeper Council
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 12 Tribes of Israel Section -->
    <div id="israel-tribes" class="py-16 relative bg-gradient-to-r from-cyber-cyan/5 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header with Biblical Context -->
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-cyan mb-6">
                    The 12 Tribes of Israel
                </h2>
                <div class="glass-card-enhanced p-6 max-w-4xl mx-auto mb-8">
                    <div class="text-cyber-cyan text-sm font-orbitron mb-3">Numbers 1:44 (KJV)</div>
                    <blockquote class="text-lg text-text-primary italic leading-relaxed mb-4">
                        "These are those that were numbered, which Moses and Aaron numbered,
                        and the princes of Israel, being twelve men: each one was for the house of his fathers."
                    </blockquote>
                    <p class="text-text-secondary leading-relaxed">
                        The covenant nations - children of Jacob/Israel who received the promises of Abraham, Isaac, and Jacob.
                        Each tribe carries unique blessings, responsibilities, and spiritual callings as outlined in Genesis 49
                        and Deuteronomy 33. Gate Keeper verification ensures the integrity of covenant membership.
                    </p>
                </div>

                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <a href="{{ url_for('tribes.israel_tribes') }}"
                       class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        📜 Detailed Tribal Study
                    </a>
                    <a href="#gate-keeper-council"
                       class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105" style="border: 2px solid var(--cyber-green);">
                        ⚖️ Gate Keeper Council
                    </a>
                </div>
            </div>

            <!-- Enhanced Tribal Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                {% for nation in covenant_nations %}
                <div class="glass-card-enhanced p-6 text-center hover:scale-105 transition-all duration-300 group cursor-pointer tribal-card"
                     onclick="showTribalEducation('{{ nation.nation_name }}', '{{ nation.tribe_name }}', '{{ nation.flag_symbol }}', '{{ nation.description }}')">
                    <div class="text-5xl mb-4 group-hover:animate-pulse">{{ nation.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-cyan mb-2 text-lg">{{ nation.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ nation.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary mb-4">{{ nation.description[:60] }}...</p>
                    <div class="mt-3 text-xs text-cyber-green font-semibold">
                        ⚖️ Gate Keeper Verification Required
                    </div>
                    <div class="mt-2 text-xs text-cyber-blue opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        🔍 Click to learn more
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Covenant Privileges Section -->
            <div class="glass-card-enhanced p-8 max-w-5xl mx-auto">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4 text-center">Covenant Privileges & Responsibilities</h3>
                <div class="text-center mb-6">
                    <p class="text-text-secondary italic">"Now therefore, if ye will obey my voice indeed, and keep my covenant, then ye shall be a peculiar treasure unto me above all people" - Exodus 19:5 (KJV)</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="font-orbitron font-bold text-cyber-purple mb-4">🎁 Covenant Blessings:</h4>
                        <ul class="space-y-3 text-sm text-text-secondary">
                            <li class="flex items-start">
                                <span class="text-cyber-purple mr-2">•</span>
                                <span>Mikvah Token allocation upon Gate Keeper verification</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-purple mr-2">•</span>
                                <span>Access to tribal governance through Voice Scrolls</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-purple mr-2">•</span>
                                <span>Participation in Yovel (Jubilee) redistributions</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-purple mr-2">•</span>
                                <span>Sabbath observance rewards and divine protection</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-purple mr-2">•</span>
                                <span>Priority access to covenant economic system</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-purple mr-2">•</span>
                                <span>Gleaning pool provisions for times of need</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-orbitron font-bold text-cyber-green mb-4">📜 Covenant Obligations:</h4>
                        <ul class="space-y-3 text-sm text-text-secondary">
                            <li class="flex items-start">
                                <span class="text-cyber-green mr-2">•</span>
                                <span>Uphold biblical principles and commandments</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-green mr-2">•</span>
                                <span>Participate faithfully in tribal governance</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-green mr-2">•</span>
                                <span>Support fellow covenant members in need</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-green mr-2">•</span>
                                <span>Maintain tribal heritage and biblical traditions</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-green mr-2">•</span>
                                <span>Contribute to community through righteous labor</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-cyber-green mr-2">•</span>
                                <span>Walk in holiness and be a light to the nations</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="text-center mt-6 p-4 bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10 rounded-lg">
                    <p class="text-sm text-text-tertiary italic">"For thou art an holy people unto the LORD thy God: the LORD thy God hath chosen thee to be a special people unto himself" - Deuteronomy 7:6 (KJV)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 12 Dukes of Edom Section -->
    <div id="edom-dukes" class="py-16 relative bg-gradient-to-r from-cyber-purple/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header with Biblical Context -->
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-purple mb-6">
                    The 12 Dukes of Edom
                </h2>
                <div class="glass-card-enhanced p-6 max-w-4xl mx-auto mb-8">
                    <div class="text-cyber-purple text-sm font-orbitron mb-3">Genesis 36:40-43 (KJV)</div>
                    <blockquote class="text-lg text-text-primary italic leading-relaxed mb-4">
                        "And these are the names of the dukes that came of Esau, according to their families,
                        after their places, by their names; duke Timnah, duke Alvah, duke Jetheth,
                        Duke Aholibamah, duke Elah, duke Pinon, Duke Kenaz, duke Teman, duke Mibzar..."
                    </blockquote>
                    <p class="text-text-secondary leading-relaxed mb-4">
                        Descendants of Esau, Jacob's twin brother, who received his own blessing and inheritance from Isaac.
                        "And by thy sword shalt thou live, and shalt serve thy brother; and it shall come to pass when thou shalt have the dominion,
                        that thou shalt break his yoke from off thy neck" - Genesis 27:40 (KJV)
                    </p>
                    <div class="text-sm text-text-tertiary">
                        The Edomites are witness nations with immediate registration privileges, representing the broader Abrahamic covenant through Isaac.
                        Though marked by complex relationships with Israel throughout Scripture, they remain brothers in the covenant witness.
                    </div>
                </div>

                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <a href="{{ url_for('tribes.edom_dukes') }}"
                       class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                       style="border: 2px solid var(--cyber-purple);">
                        👑 Detailed Edom Study
                    </a>
                    <button onclick="showBiblicalContext('edom')"
                            class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                            style="border: 2px solid var(--cyber-blue);">
                        📖 Biblical History
                    </button>
                </div>
            </div>

            <!-- Enhanced Dukes Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                {% for duke in edom_dukes %}
                <div class="glass-card-enhanced p-6 text-center hover:scale-105 transition-all duration-300 group cursor-pointer tribal-card"
                     onclick="showTribalEducation('{{ duke.nation_name }}', '{{ duke.tribe_name }}', '{{ duke.flag_symbol }}', '{{ duke.description }}')">
                    <div class="text-5xl mb-4 group-hover:animate-pulse">{{ duke.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-purple mb-2 text-lg">{{ duke.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ duke.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary mb-4">{{ duke.description[:60] }}...</p>
                    <div class="mt-3 text-xs text-cyber-blue font-semibold">
                        ⚡ Immediate Inscription
                    </div>
                    <div class="mt-2 text-xs text-cyber-purple opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        🔍 Click to learn more
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Edom Historical Context -->
            <div class="glass-card-enhanced p-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6 text-center">Edom in Biblical History</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-orbitron font-bold text-cyber-cyan mb-3">🏛️ Historical Significance:</h4>
                        <ul class="space-y-2 text-sm text-text-secondary">
                            <li>• Twin brother of Jacob (Israel)</li>
                            <li>• Received blessing from Isaac (Genesis 27:39-40)</li>
                            <li>• Established kingdom before Israel had kings</li>
                            <li>• Strategic location controlling trade routes</li>
                            <li>• Prophetic significance in end times</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-orbitron font-bold text-cyber-green mb-3">⚡ Witness Nation Status:</h4>
                        <ul class="space-y-2 text-sm text-text-secondary">
                            <li>• Immediate inscription without Gate Keeper review</li>
                            <li>• Access to witness nation governance</li>
                            <li>• Participation in biblical tokenomics</li>
                            <li>• Recognition of Abrahamic heritage</li>
                            <li>• Role in prophetic fulfillment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 12 Princes of Ishmael Section -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-blue mb-4">
                    The 12 Princes of Ishmael
                </h2>
                <div class="glass-card-enhanced p-6 max-w-4xl mx-auto mb-8">
                    <div class="text-cyber-blue text-sm font-orbitron mb-3">Genesis 17:20 (KJV)</div>
                    <blockquote class="text-lg text-text-primary italic leading-relaxed mb-4">
                        "And as for Ishmael, I have heard thee: Behold, I have blessed him, and will make him fruitful,
                        and will multiply him exceedingly; twelve princes shall he beget, and I will make him a great nation."
                    </blockquote>
                    <div class="text-text-secondary">
                        Sons of Abraham through Hagar - blessed witness nation with immediate registration privileges
                    </div>
                </div>
                <a href="{{ url_for('tribes.ishmael_princes') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                   style="border: 2px solid var(--cyber-blue);">
                    🏜️ Learn More About Ishmael
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
                {% for prince in ishmael_princes %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">{{ prince.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-blue mb-2">{{ prince.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ prince.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary">{{ prince.description[:50] }}...</p>
                    <div class="mt-3 text-xs text-cyber-blue">
                        ⚡ Immediate Inscription
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Hamitic Nations Section -->
    {% if hamitic_nations %}
    <div class="py-16 relative bg-gradient-to-r from-cyber-green/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-green mb-4">
                    Hamitic Nations
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    Sons of Ham - witness nations with immediate inscription (Genesis 10:6)
                </p>
                <a href="{{ url_for('tribes.hamitic_nations') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                   style="border: 2px solid var(--cyber-green);">
                    🏺 Learn More About Ham's Descendants
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {% for nation in hamitic_nations %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">{{ nation.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-green mb-2">{{ nation.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ nation.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary">{{ nation.description[:50] }}...</p>
                    <div class="mt-3 text-xs text-cyber-blue">
                        ⚡ Immediate Inscription
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Registration Call to Action -->
    <div class="py-20 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-card-premium p-12">
                <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Ready to Discover Your Lineage?
                </h2>
                <p class="text-lg text-text-secondary mb-8 leading-relaxed">
                    Begin your covenant identity journey through Eden Mode. Discover your biblical heritage 
                    and join the appropriate tribal community based on your lineage.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('eden_mode.step1') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        🌟 Begin Eden Mode Registration
                    </a>
                    <a href="/governance/public"
                       class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        ⚖️ View Gate Keeper Council
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Gate Keeper Council Section -->
    <div id="gate-keeper-council" class="py-16 relative bg-gradient-to-r from-cyber-green/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-green mb-6">
                    Council of 12 Gate Keepers
                </h2>
                <div class="glass-card-enhanced p-6 max-w-4xl mx-auto mb-8">
                    <div class="text-cyber-green text-sm font-orbitron mb-3">Nehemiah 7:1-3 (KJV)</div>
                    <blockquote class="text-lg text-text-primary italic leading-relaxed mb-4">
                        "Now it came to pass, when the wall was built, and I had set up the doors,
                        and the porters and the singers and the Levites were appointed,
                        That I gave my brother Hanani, and Hananiah the ruler of the palace,
                        charge over Jerusalem: for he was a faithful man, and feared God above many."
                    </blockquote>
                    <p class="text-text-secondary leading-relaxed">
                        The sacred council responsible for verifying Israelite covenant identities.
                        Each of the 12 Tribes of Israel has one Gate Keeper representative who votes
                        on identity verification proposals. This ensures that covenant identity claims
                        are properly evaluated by those with tribal authority and biblical knowledge.
                    </p>
                </div>

                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <a href="{{ url_for('governance.council_overview') }}"
                       class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        ⚖️ View Full Council
                    </a>
                    <a href="{{ url_for('governance.public_governance') }}"
                       class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                       style="border: 2px solid var(--cyber-cyan);">
                        📊 Governance Transparency
                    </a>
                    <a href="{{ url_for('faq.gate_keepers') }}"
                       class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                       style="border: 2px solid var(--cyber-blue);">
                        ❓ Gate Keeper FAQ
                    </a>
                </div>
            </div>

            <!-- Gate Keeper Responsibilities -->
            <div class="glass-card-enhanced p-8 max-w-6xl mx-auto">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-green mb-6 text-center">Gate Keeper Responsibilities</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-4xl mb-4">⚖️</div>
                        <h4 class="font-orbitron font-bold text-cyber-cyan mb-3">Identity Verification</h4>
                        <p class="text-sm text-text-secondary">
                            Review and vote on Israelite heritage claims, ensuring biblical standards
                            and tribal authenticity are maintained.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl mb-4">🛡️</div>
                        <h4 class="font-orbitron font-bold text-cyber-purple mb-3">Covenant Protection</h4>
                        <p class="text-sm text-text-secondary">
                            Safeguard the integrity of covenant membership and prevent fraudulent
                            claims that would dilute tribal heritage.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl mb-4">📜</div>
                        <h4 class="font-orbitron font-bold text-cyber-blue mb-3">Tribal Governance</h4>
                        <p class="text-sm text-text-secondary">
                            Represent their respective tribes in governance decisions and
                            Voice Scroll voting processes.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tribal Education Modal -->
<div id="tribal-education-modal" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="glass-card-enhanced max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 id="modal-title" class="text-2xl font-orbitron font-bold text-cyber-cyan"></h3>
                    <button onclick="closeTribalModal()" class="text-text-secondary hover:text-cyber-red transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div id="modal-content" class="space-y-4">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tribal Education Functions
function showTribalEducation(nationName, tribeName, symbol, description) {
    const modal = document.getElementById('tribal-education-modal');
    const title = document.getElementById('modal-title');
    const content = document.getElementById('modal-content');

    title.textContent = `${symbol} ${nationName}`;

    content.innerHTML = `
        <div class="space-y-6">
            <div class="text-center">
                <div class="text-6xl mb-4">${symbol}</div>
                <h4 class="text-xl font-orbitron font-bold text-cyber-purple mb-2">${tribeName}</h4>
                <p class="text-text-secondary">${description}</p>
            </div>

            <div class="glass-card p-4">
                <h5 class="font-orbitron font-bold text-cyber-cyan mb-3">Biblical Heritage</h5>
                <p class="text-sm text-text-secondary">
                    This tribal lineage traces back to the biblical patriarchs and carries specific
                    blessings, responsibilities, and prophetic significance as outlined in Scripture.
                </p>
            </div>

            <div class="glass-card p-4">
                <h5 class="font-orbitron font-bold text-cyber-green mb-3">Verification Process</h5>
                <p class="text-sm text-text-secondary">
                    ${nationName.includes('Israel') ?
                        'Requires Gate Keeper verification with genealogical evidence and tribal documentation.' :
                        'Immediate inscription available for witness nations with supporting documentation.'
                    }
                </p>
            </div>

            <div class="text-center">
                <a href="/auth/register" class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    🔐 Begin Registration
                </a>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
}

function showBiblicalContext(nation) {
    const modal = document.getElementById('tribal-education-modal');
    const title = document.getElementById('modal-title');
    const content = document.getElementById('modal-content');

    let contextData = {};

    if (nation === 'edom') {
        contextData = {
            title: '📖 Edom in Biblical History',
            verses: [
                {
                    reference: 'Genesis 25:30',
                    text: 'And Esau said to Jacob, Feed me, I pray thee, with that same red pottage; for I am faint: therefore was his name called Edom.'
                },
                {
                    reference: 'Genesis 27:39-40',
                    text: 'And Isaac his father answered and said unto him, Behold, thy dwelling shall be the fatness of the earth, and of the dew of heaven from above; And by thy sword shalt thou live, and shalt serve thy brother; and it shall come to pass when thou shalt have the dominion, that thou shalt break his yoke from off thy neck.'
                }
            ],
            significance: 'Edom represents the complex relationship between covenant and non-covenant peoples, showing how God\'s sovereignty extends beyond Israel while maintaining distinct roles and destinies.'
        };
    }

    title.textContent = contextData.title;

    content.innerHTML = `
        <div class="space-y-6">
            ${contextData.verses.map(verse => `
                <div class="glass-card p-4">
                    <div class="text-cyber-cyan text-sm font-orbitron mb-2">${verse.reference} (KJV)</div>
                    <blockquote class="text-text-primary italic">"${verse.text}"</blockquote>
                </div>
            `).join('')}

            <div class="glass-card p-4">
                <h5 class="font-orbitron font-bold text-cyber-purple mb-3">Theological Significance</h5>
                <p class="text-sm text-text-secondary">${contextData.significance}</p>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
}

function closeTribalModal() {
    const modal = document.getElementById('tribal-education-modal');
    modal.classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('tribal-education-modal');
    if (event.target === modal) {
        closeTribalModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeTribalModal();
    }
});

// Smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

{% endblock %}
