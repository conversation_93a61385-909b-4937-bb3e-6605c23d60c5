#!/usr/bin/env python3
"""
Fix Database Schema Issues for ONNYX Platform
"""

import sqlite3
import sys
import os
import time

def fix_identities_table():
    """Fix missing columns in identities table"""
    
    print("🔧 Fixing Identities Table Schema")
    print("=" * 40)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current table structure
        cursor.execute('PRAGMA table_info(identities)')
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        # Add missing columns
        missing_columns = [
            ('last_active_timestamp', 'INTEGER'),
            ('last_transaction_height', 'INTEGER DEFAULT 0'),
            ('activity_score', 'REAL DEFAULT 0.0'),
            ('covenant_status', 'TEXT DEFAULT "active"'),
            ('verification_timestamp', 'INTEGER'),
            ('gate_keeper_verified', 'BOOLEAN DEFAULT 0'),
            ('lineage_verified', 'BOOLEAN DEFAULT 0')
        ]
        
        added_columns = 0
        for column_name, column_def in missing_columns:
            if column_name not in column_names:
                print(f"Adding column: {column_name}")
                cursor.execute(f'ALTER TABLE identities ADD COLUMN {column_name} {column_def}')
                added_columns += 1
                print(f"✅ Added {column_name} column")
            else:
                print(f"✅ {column_name} column already exists")
        
        if added_columns > 0:
            conn.commit()
            print(f"✅ Added {added_columns} new columns to identities table")
        else:
            print("✅ All required columns already exist")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing identities table: {e}")
        return False

def create_missing_tables():
    """Create missing tables"""
    
    print("\n🏗️ Creating Missing Tables")
    print("=" * 35)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create etzem_history table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS etzem_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                old_score INTEGER NOT NULL,
                new_score INTEGER NOT NULL,
                change_reason TEXT NOT NULL,
                change_amount INTEGER NOT NULL,
                timestamp INTEGER NOT NULL,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        print("✅ Created etzem_history table")
        
        # Create gate_keeper_proposals table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS gate_keeper_proposals (
                proposal_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                proposer_id TEXT NOT NULL,
                proposal_type TEXT NOT NULL DEFAULT 'verification',
                proposal_data TEXT DEFAULT '{}',
                status TEXT DEFAULT 'pending',
                votes_for INTEGER DEFAULT 0,
                votes_against INTEGER DEFAULT 0,
                created_at INTEGER NOT NULL,
                expires_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (proposer_id) REFERENCES identities(identity_id)
            )
        """)
        print("✅ Created gate_keeper_proposals table")
        
        # Create new_moon_sabbaths table for lunar calendar tracking
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS new_moon_sabbaths (
                moon_id TEXT PRIMARY KEY,
                new_moon_timestamp INTEGER NOT NULL,
                sabbath_start INTEGER NOT NULL,
                sabbath_end INTEGER NOT NULL,
                lunar_month INTEGER NOT NULL,
                hebrew_month TEXT NOT NULL,
                biblical_reference TEXT DEFAULT 'Numbers 28:11',
                active BOOLEAN DEFAULT 1,
                created_at INTEGER NOT NULL
            )
        """)
        print("✅ Created new_moon_sabbaths table")
        
        # Create biblical_calendar table for Hebrew calendar calculations
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biblical_calendar (
                calendar_id TEXT PRIMARY KEY,
                hebrew_year INTEGER NOT NULL,
                hebrew_month INTEGER NOT NULL,
                hebrew_day INTEGER NOT NULL,
                gregorian_date TEXT NOT NULL,
                gregorian_timestamp INTEGER NOT NULL,
                is_feast_day BOOLEAN DEFAULT 0,
                feast_name TEXT,
                biblical_reference TEXT,
                created_at INTEGER NOT NULL
            )
        """)
        print("✅ Created biblical_calendar table")
        
        # Create timezone_locks table for sabbath enforcement
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS timezone_locks (
                lock_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                locked_timezone TEXT NOT NULL,
                registration_location TEXT,
                lock_timestamp INTEGER NOT NULL,
                lock_reason TEXT DEFAULT 'sabbath_enforcement',
                can_override BOOLEAN DEFAULT 0,
                created_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        print("✅ Created timezone_locks table")
        
        conn.commit()
        conn.close()
        
        print("\n✅ All missing tables created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error creating missing tables: {e}")
        return False

def update_existing_data():
    """Update existing data with proper values"""
    
    print("\n📊 Updating Existing Data")
    print("=" * 30)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        current_time = int(time.time())
        
        # Update identities with missing timestamps
        cursor.execute("""
            UPDATE identities 
            SET last_active_timestamp = ?,
                verification_timestamp = created_at
            WHERE last_active_timestamp IS NULL
        """, (current_time,))
        
        updated_identities = cursor.rowcount
        print(f"✅ Updated {updated_identities} identities with timestamps")
        
        # Initialize etzem_history for existing identities
        cursor.execute("""
            SELECT identity_id, etzem_score 
            FROM identities 
            WHERE identity_id NOT IN (SELECT DISTINCT identity_id FROM etzem_history)
        """)
        
        identities_without_history = cursor.fetchall()
        
        for identity_id, etzem_score in identities_without_history:
            cursor.execute("""
                INSERT INTO etzem_history 
                (identity_id, old_score, new_score, change_reason, change_amount, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (identity_id, 0, etzem_score or 100, 'Initial score', etzem_score or 100, current_time))
        
        print(f"✅ Created etzem_history for {len(identities_without_history)} identities")
        
        # Lock timezones for existing sabbath observers
        cursor.execute("""
            SELECT identity_id, preferred_sabbath_timezone, created_at
            FROM identities 
            WHERE sabbath_observer = 1 
            AND identity_id NOT IN (SELECT DISTINCT identity_id FROM timezone_locks)
        """)
        
        sabbath_observers = cursor.fetchall()
        
        for identity_id, timezone, created_at in sabbath_observers:
            lock_id = f"timezone_lock_{identity_id}_{current_time}"
            cursor.execute("""
                INSERT INTO timezone_locks 
                (lock_id, identity_id, locked_timezone, lock_timestamp, 
                 lock_reason, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (lock_id, identity_id, timezone or 'UTC', created_at or current_time,
                  'sabbath_enforcement', current_time))
        
        print(f"✅ Created timezone locks for {len(sabbath_observers)} sabbath observers")
        
        conn.commit()
        conn.close()
        
        print("\n✅ Existing data updated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error updating existing data: {e}")
        return False

def test_database_fixes():
    """Test that all database fixes are working"""
    
    print("\n🧪 Testing Database Fixes")
    print("=" * 30)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test identities table columns
        cursor.execute('PRAGMA table_info(identities)')
        identities_columns = [col[1] for col in cursor.fetchall()]
        
        required_identity_columns = [
            'last_active_timestamp', 'last_transaction_height', 'activity_score',
            'covenant_status', 'verification_timestamp', 'gate_keeper_verified'
        ]
        
        missing_identity_columns = []
        for col in required_identity_columns:
            if col in identities_columns:
                print(f"✅ identities.{col}: Present")
            else:
                print(f"❌ identities.{col}: Missing")
                missing_identity_columns.append(col)
        
        # Test required tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = [
            'etzem_history', 'gate_keeper_proposals', 'new_moon_sabbaths',
            'biblical_calendar', 'timezone_locks'
        ]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ {table} table: Present")
            else:
                print(f"❌ {table} table: Missing")
                missing_tables.append(table)
        
        # Test data integrity
        cursor.execute("SELECT COUNT(*) FROM etzem_history")
        etzem_history_count = cursor.fetchone()[0]
        print(f"✅ Etzem history records: {etzem_history_count}")
        
        cursor.execute("SELECT COUNT(*) FROM timezone_locks")
        timezone_locks_count = cursor.fetchone()[0]
        print(f"✅ Timezone locks: {timezone_locks_count}")
        
        conn.close()
        
        if missing_identity_columns or missing_tables:
            print(f"\n❌ Some issues remain")
            return False
        else:
            print(f"\n✅ All database fixes verified")
            return True
        
    except Exception as e:
        print(f"❌ Error testing database fixes: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing Database Schema Issues")
    print("=" * 50)
    
    # Fix identities table
    identities_fixed = fix_identities_table()
    
    # Create missing tables
    tables_created = create_missing_tables()
    
    # Update existing data
    data_updated = update_existing_data()
    
    # Test the fixes
    fixes_tested = test_database_fixes()
    
    print("\n📊 Fix Results:")
    print("=" * 25)
    
    if identities_fixed:
        print("✅ Identities table: Fixed")
    else:
        print("❌ Identities table: Issues remain")
    
    if tables_created:
        print("✅ Missing tables: Created")
    else:
        print("❌ Missing tables: Issues remain")
    
    if data_updated:
        print("✅ Data updates: Successful")
    else:
        print("❌ Data updates: Issues remain")
    
    if fixes_tested:
        print("✅ Testing: All fixes verified")
    else:
        print("❌ Testing: Some issues remain")
    
    overall_success = identities_fixed and tables_created and data_updated and fixes_tested
    
    if overall_success:
        print("\n🎉 SUCCESS: All database schema issues fixed!")
        print("   - Missing columns added to identities table")
        print("   - Missing tables created (etzem_history, gate_keeper_proposals, etc.)")
        print("   - Existing data updated with proper values")
        print("   - Timezone locks implemented for sabbath enforcement")
        print("   - All fixes tested and verified")
    else:
        print("\n⚠️ Some issues remain - check details above")
    
    sys.exit(0 if overall_success else 1)
