#!/usr/bin/env python3
"""
Test Sela Registration Flow
"""

import requests
import sys

def test_sela_registration_flow():
    """Test the Sela registration flow for logged-in users"""
    print("🧪 Testing Sela Registration Flow")
    print("=" * 50)
    
    try:
        # Create session and login as genesis admin
        session = requests.Session()
        
        # Login
        login_data = {
            'email': '<EMAIL>',
            'password': 'Genesis2024!'
        }
        
        login_response = session.post(
            'http://127.0.0.1:5000/auth/login',
            data=login_data,
            timeout=10
        )
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print("✅ Logged in as genesis admin")
        
        # Test the register sela route (should redirect to sela create form)
        register_response = session.get(
            'http://127.0.0.1:5000/auth/register/sela',
            allow_redirects=False,
            timeout=10
        )
        
        print(f"Register Sela response status: {register_response.status_code}")
        
        if register_response.status_code == 302:
            redirect_location = register_response.headers.get('Location', '')
            print(f"Redirect location: {redirect_location}")
            
            if '/sela/create' in redirect_location:
                print("✅ Correctly redirects to Sela creation form")

                # Follow the redirect to test the create form
                full_url = 'http://127.0.0.1:5000' + redirect_location
                create_response = session.get(full_url, timeout=10)
                
                if create_response.status_code == 200:
                    print("✅ Sela creation form loads successfully")
                    
                    # Check if the form contains expected elements
                    if 'business' in create_response.text.lower() and 'sela' in create_response.text.lower():
                        print("✅ Form contains expected Sela registration content")
                        return True
                    else:
                        print("❌ Form doesn't contain expected content")
                        return False
                else:
                    print(f"❌ Sela creation form failed to load: {create_response.status_code}")
                    return False
            else:
                print(f"❌ Incorrect redirect location: {redirect_location}")
                return False
        else:
            print(f"❌ Expected redirect (302), got: {register_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Sela registration: {e}")
        return False

def test_sela_directory_access():
    """Test accessing the Sela directory page"""
    print("\n🌐 Testing Sela Directory Access")
    print("=" * 40)
    
    try:
        # Test the Sela directory page
        response = requests.get('http://127.0.0.1:5000/sela/', timeout=10)
        
        if response.status_code == 200:
            print("✅ Sela directory loads successfully")
            
            # Check for Register Validator button
            if 'Register Validator' in response.text or 'Register as Validator' in response.text:
                print("✅ Register Validator button found")
                return True
            else:
                print("❌ Register Validator button not found")
                return False
        else:
            print(f"❌ Sela directory failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Sela directory: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Sela Registration Functionality")
    print("=" * 60)
    
    # Test directory access
    directory_ok = test_sela_directory_access()
    
    # Test registration flow
    registration_ok = test_sela_registration_flow()
    
    print("\n📊 Final Results:")
    print("=" * 30)
    
    if directory_ok:
        print("✅ Sela directory: OK")
    else:
        print("❌ Sela directory: ISSUES")
    
    if registration_ok:
        print("✅ Registration flow: OK")
    else:
        print("❌ Registration flow: ISSUES")
    
    overall_success = directory_ok and registration_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: Sela registration flow is working!")
        print("   - Directory page loads with Register Validator button")
        print("   - Registration redirects to proper Sela creation form")
        print("   - Form loads successfully for logged-in users")
    else:
        print("\n⚠️ Some issues found - check details above")
    
    sys.exit(0 if overall_success else 1)
