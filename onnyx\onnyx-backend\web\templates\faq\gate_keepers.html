{% extends "base.html" %}

{% block title %}Gate Keepers FAQ - ONNYX Platform{% endblock %}

{% block head %}
<style>
.faq-container {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.05), rgba(154, 0, 255, 0.05));
    min-height: 100vh;
    padding: 2rem 0;
}

.faq-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.faq-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.4);
}

.faq-question {
    background: rgba(0, 255, 247, 0.1);
    border-bottom: 1px solid rgba(0, 255, 247, 0.3);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: rgba(0, 255, 247, 0.15);
}

.faq-answer {
    padding: 1.5rem;
    background: rgba(154, 0, 255, 0.05);
    border-top: 1px solid rgba(154, 0, 255, 0.2);
}

.biblical-quote {
    background: rgba(0, 255, 247, 0.1);
    border-left: 4px solid var(--cyber-cyan);
    padding: 1rem;
    border-radius: 0.5rem;
    font-style: italic;
    margin: 1.5rem 0;
}

.gate-keeper-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: bold;
    display: inline-block;
}

.expand-icon {
    transition: transform 0.3s ease;
}

.expanded .expand-icon {
    transform: rotate(180deg);
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 50%;
    opacity: 0.7;
    animation: float 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-15px) rotate(180deg); opacity: 1; }
}
</style>
{% endblock %}

{% block content %}
<div class="faq-container relative">
    <div class="floating-particles"></div>
    
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl mb-6">
                <span class="text-3xl">🚪</span>
            </div>
            <h1 class="text-4xl md:text-6xl font-orbitron font-bold text-white mb-4">Gate Keepers FAQ</h1>
            <p class="text-xl text-text-secondary max-w-4xl mx-auto mb-6">
                "And I will give you pastors according to mine heart, which shall feed you with knowledge and understanding" - Jeremiah 3:15 (KJV)
            </p>
            <div class="text-lg text-text-tertiary">
                Understanding the sacred role of covenant guardians and identity verification
            </div>
        </div>

        <!-- Biblical Foundation -->
        <div class="faq-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6 text-center">Biblical Foundation of Gate Keeping</h2>
            <div class="biblical-quote">
                "And the LORD said unto Moses, Take thee Joshua the son of Nun, a man in whom is the spirit, and lay thine hand upon him; And set him before Eleazar the priest, and before all the congregation; and give him a charge in their sight" - Numbers 27:18-19 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-4xl mb-4">👑</div>
                    <h3 class="text-xl font-semibold text-cyber-cyan mb-2">Divine Authority</h3>
                    <p class="text-text-secondary">"And Moses chose able men out of all Israel, and made them heads over the people" - Exodus 18:25 (KJV)</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">⚖️</div>
                    <h3 class="text-xl font-semibold text-cyber-purple mb-2">Righteous Judgment</h3>
                    <p class="text-text-secondary">"Judges and officers shalt thou make thee in all thy gates" - Deuteronomy 16:18 (KJV)</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">🛡️</div>
                    <h3 class="text-xl font-semibold text-cyber-green mb-2">Covenant Protection</h3>
                    <p class="text-text-secondary">"And they shall keep thy charge, and the charge of all the tabernacle" - Numbers 18:3 (KJV)</p>
                </div>
            </div>
        </div>

        <!-- Gate Keeper Role -->
        <div class="faq-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">The Sacred Role</h2>
            <div class="biblical-quote">
                "Moreover thou shalt provide out of all the people able men, such as fear God, men of truth, hating covetousness; and place such over them" - Exodus 18:21 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Qualifications</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Fear of the LORD and biblical wisdom</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Verified tribal lineage and covenant standing</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Demonstrated righteousness and integrity</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-cyan mr-3">•</span>
                            <span class="text-text-secondary">Community recognition and tribal election</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-4">Responsibilities</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Verify covenant identity applications</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Judge according to biblical principles</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Protect covenant community integrity</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Serve with humility and faithfulness</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-8 text-center">Frequently Asked Questions</h2>
            
            {% for faq in faqs %}
            <div class="faq-card" data-faq="{{ loop.index }}">
                <div class="faq-question" onclick="toggleFAQ({{ loop.index }})">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-orbitron font-bold text-white">{{ faq.question }}</h3>
                        <svg class="w-6 h-6 text-cyber-cyan expand-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div class="faq-answer" id="answer-{{ loop.index }}" style="display: none;">
                    <p class="text-text-secondary leading-relaxed">{{ faq.answer }}</p>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Verification Process -->
        <div class="faq-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">Verification Process</h2>
            <div class="biblical-quote">
                "But let every man prove his own work, and then shall he have rejoicing in himself alone" - Galatians 6:4 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">📝</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">1. Application</h3>
                    <p class="text-sm text-text-secondary">Submit identity claim with supporting evidence</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🔍</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">2. Review</h3>
                    <p class="text-sm text-text-secondary">Gate Keepers examine evidence and lineage</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-cyber-green rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🗳️</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">3. Vote</h3>
                    <p class="text-sm text-text-secondary">Council votes within 30 days</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-green to-cyber-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">✅</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-green mb-2">4. Decision</h3>
                    <p class="text-sm text-text-secondary">7 of 12 votes required for approval</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="faq-card p-8 text-center">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">Seek Covenant Identity</h2>
            <div class="biblical-quote">
                "Ask, and it shall be given you; seek, and ye shall find; knock, and it shall be opened unto you" - Matthew 7:7 (KJV)
            </div>
            <p class="text-lg text-text-secondary mb-6">
                Ready to discover your biblical heritage and join the covenant community? 
                Begin your journey through Eden Mode and submit your identity for Gate Keeper verification.
            </p>
            <div class="flex justify-center space-x-4">
                <a href="{{ url_for('onboarding.eden_mode') }}" class="bg-gradient-to-r from-cyber-cyan to-cyber-purple text-onyx-black font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    Begin Eden Mode
                </a>
                <a href="{{ url_for('governance.public_governance') }}" class="bg-gradient-to-r from-cyber-purple to-cyber-blue text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    View Gate Keepers
                </a>
            </div>
        </div>

        <!-- Navigation -->
        <div class="text-center mt-8">
            <div class="flex justify-center space-x-6">
                <a href="{{ url_for('faq.biblical_blockchain') }}" class="text-cyber-cyan hover:text-cyan-400 transition-colors">
                    Biblical Blockchain FAQ
                </a>
                <a href="{{ url_for('faq.tribal_lineage') }}" class="text-cyber-purple hover:text-purple-400 transition-colors">
                    Tribal Lineage FAQ
                </a>
                <a href="{{ url_for('home.index') }}" class="text-cyber-green hover:text-green-400 transition-colors">
                    Return Home
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFAQ(index) {
    const answer = document.getElementById(`answer-${index}`);
    const card = document.querySelector(`[data-faq="${index}"]`);
    
    if (answer.style.display === 'none') {
        answer.style.display = 'block';
        card.classList.add('expanded');
    } else {
        answer.style.display = 'none';
        card.classList.remove('expanded');
    }
}

function addFloatingParticles() {
    const container = document.querySelector('.floating-particles');
    if (container) {
        for (let i = 0; i < 6; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particle.style.animationDuration = (3 + Math.random() * 2) + 's';
            container.appendChild(particle);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    addFloatingParticles();
});
</script>
{% endblock %}
