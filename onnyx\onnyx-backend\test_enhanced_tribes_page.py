#!/usr/bin/env python3
"""
Test Enhanced Tribes Overview Page
"""

import requests
import sys

def test_tribes_page_enhancements():
    """Test that the tribes page has enhanced design and biblical education content"""
    print("🧪 Testing Enhanced Tribes Overview Page")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5000/tribes/overview', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for enhanced design elements
            design_checks = [
                ('Enhanced hero section', 'Biblical Tribes & Nations' in content),
                ('Biblical foundation quote', 'Genesis 35:10-12' in content),
                ('Quick navigation', 'href="#israel-tribes"' in content),
                ('Animated background', 'animate-pulse' in content),
                ('Glass card enhanced', 'glass-card-enhanced' in content),
                ('Covenant privileges section', 'Covenant Privileges & Responsibilities' in content)
            ]
            
            # Check for biblical education content
            education_checks = [
                ('Israel tribes education', 'Numbers 1:44' in content),
                ('Edom biblical context', 'Genesis 36:40-43' in content),
                ('Gate Keeper council', 'Council of 12 Gate Keepers' in content),
                ('Nehemiah reference', 'Nehemiah 7:1-3' in content),
                ('Interactive elements', 'showTribalEducation' in content),
                ('Biblical context modal', 'showBiblicalContext' in content)
            ]
            
            # Check for interactive features
            interactive_checks = [
                ('Tribal education modal', 'tribal-education-modal' in content),
                ('Click handlers', 'onclick="showTribalEducation' in content),
                ('Modal functions', 'closeTribalModal' in content),
                ('Smooth scrolling', 'scrollIntoView' in content),
                ('Keyboard support', 'event.key === \'Escape\'' in content)
            ]
            
            all_checks = design_checks + education_checks + interactive_checks
            passed = 0
            
            print("Design Enhancements:")
            for check_name, result in design_checks:
                if result:
                    print(f"  ✅ {check_name}")
                    passed += 1
                else:
                    print(f"  ❌ {check_name}")
            
            print("\nBiblical Education Content:")
            for check_name, result in education_checks:
                if result:
                    print(f"  ✅ {check_name}")
                    passed += 1
                else:
                    print(f"  ❌ {check_name}")
            
            print("\nInteractive Features:")
            for check_name, result in interactive_checks:
                if result:
                    print(f"  ✅ {check_name}")
                    passed += 1
                else:
                    print(f"  ❌ {check_name}")
            
            print(f"\nOverall: {passed}/{len(all_checks)} enhancements found")
            return passed >= len(all_checks) * 0.8  # At least 80% should be present
            
        else:
            print(f"❌ Tribes page failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing tribes page: {e}")
        return False

def test_gate_keeper_council_links():
    """Test that Gate Keeper Council links work"""
    print("\n🔗 Testing Gate Keeper Council Links")
    print("=" * 40)
    
    try:
        # Test governance council overview
        response = requests.get('http://127.0.0.1:5000/governance/council', timeout=10)
        
        if response.status_code == 200:
            print("✅ Gate Keeper Council page accessible")
            
            # Check for council content
            content = response.text
            if 'Council of 12' in content or 'Gate Keeper' in content:
                print("✅ Council content found")
                return True
            else:
                print("⚠️ Council page loads but content may be incomplete")
                return True
        else:
            print(f"❌ Gate Keeper Council page error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Gate Keeper links: {e}")
        return False

def test_biblical_education_accessibility():
    """Test that biblical education content is accessible"""
    print("\n📖 Testing Biblical Education Accessibility")
    print("=" * 45)
    
    try:
        response = requests.get('http://127.0.0.1:5000/tribes/overview', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for biblical references
            biblical_refs = [
                'Genesis 35:10-12',
                'Numbers 1:44',
                'Genesis 36:40-43',
                'Nehemiah 7:1-3'
            ]
            
            found_refs = 0
            for ref in biblical_refs:
                if ref in content:
                    print(f"✅ Biblical reference found: {ref}")
                    found_refs += 1
                else:
                    print(f"❌ Biblical reference missing: {ref}")
            
            # Check for educational structure
            educational_elements = [
                'blockquote',  # Bible verses
                'Covenant Privileges',
                'Covenant Responsibilities',
                'Historical Significance',
                'Witness Nation Status'
            ]
            
            found_elements = 0
            for element in educational_elements:
                if element in content:
                    print(f"✅ Educational element found: {element}")
                    found_elements += 1
                else:
                    print(f"❌ Educational element missing: {element}")
            
            total_found = found_refs + found_elements
            total_expected = len(biblical_refs) + len(educational_elements)
            
            print(f"\nBiblical education content: {total_found}/{total_expected} elements found")
            return total_found >= total_expected * 0.8
            
        else:
            print(f"❌ Could not access tribes page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing biblical education: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Enhanced Tribes Overview Page")
    print("=" * 60)
    
    # Test page enhancements
    enhancements_ok = test_tribes_page_enhancements()
    
    # Test Gate Keeper links
    links_ok = test_gate_keeper_council_links()
    
    # Test biblical education
    education_ok = test_biblical_education_accessibility()
    
    print("\n📊 Final Results:")
    print("=" * 30)
    
    if enhancements_ok:
        print("✅ Page enhancements: Working correctly")
    else:
        print("❌ Page enhancements: Issues found")
    
    if links_ok:
        print("✅ Gate Keeper links: Working correctly")
    else:
        print("❌ Gate Keeper links: Issues found")
    
    if education_ok:
        print("✅ Biblical education: Content accessible")
    else:
        print("❌ Biblical education: Content issues")
    
    overall_success = enhancements_ok and links_ok and education_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: Enhanced tribes overview page is working!")
        print("   - Immersive design with biblical context")
        print("   - Interactive tribal education modals")
        print("   - Gate Keeper Council integration")
        print("   - Comprehensive biblical references")
        print("   - Smooth user experience with animations")
    else:
        print("\n⚠️ Some issues found - check details above")
    
    sys.exit(0 if overall_success else 1)
