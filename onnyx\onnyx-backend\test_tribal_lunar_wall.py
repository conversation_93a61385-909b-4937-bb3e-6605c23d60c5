#!/usr/bin/env python3
"""
Test Tribal Lunar Wall Implementation
"""

import requests
import sys
import os
import time

def test_tribal_lunar_wall_route():
    """Test that the Tribal Lunar Wall route is accessible"""
    print("🌙 Testing Tribal Lunar Wall Route")
    print("=" * 40)
    
    try:
        # Test the route
        response = requests.get('http://127.0.0.1:5000/explorer/tribal-lunar-wall', timeout=10)
        
        if response.status_code == 200:
            print("✅ Tribal Lunar Wall route: Accessible")
            
            # Check for key elements in the response
            content = response.text
            
            key_elements = [
                'TRIBAL LUNAR WALL',
                'Biblical Calendar',
                'Hebrew Calendar',
                'New Moon',
                'Sabbath',
                'tribal-lunar-wall.css',
                'tribal-lunar-wall.js',
                'nightModeToggle',
                'tribeSelect',
                'calendarGrid',
                'torahScrollPopup'
            ]
            
            found_elements = []
            missing_elements = []
            
            for element in key_elements:
                if element in content:
                    found_elements.append(element)
                    print(f"✅ Found: {element}")
                else:
                    missing_elements.append(element)
                    print(f"❌ Missing: {element}")
            
            if len(found_elements) >= len(key_elements) * 0.8:  # 80% threshold
                print(f"\n✅ Tribal Lunar Wall content: Good ({len(found_elements)}/{len(key_elements)} elements found)")
                return True
            else:
                print(f"\n⚠️ Tribal Lunar Wall content: Incomplete ({len(found_elements)}/{len(key_elements)} elements found)")
                return False
        else:
            print(f"❌ Tribal Lunar Wall route: HTTP {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Tribal Lunar Wall route: {e}")
        return False

def test_static_files():
    """Test that static files are accessible"""
    print("\n📁 Testing Static Files")
    print("=" * 30)
    
    static_files = [
        '/static/css/tribal_lunar_wall.css',
        '/static/js/tribal_lunar_wall.js'
    ]
    
    files_ok = 0
    
    for file_path in static_files:
        try:
            response = requests.get(f'http://127.0.0.1:5000{file_path}', timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {file_path}: Accessible")
                files_ok += 1
            else:
                print(f"❌ {file_path}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {file_path}: Error - {e}")
    
    if files_ok == len(static_files):
        print(f"\n✅ All static files accessible")
        return True
    else:
        print(f"\n⚠️ {files_ok}/{len(static_files)} static files accessible")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\n📂 Testing File Structure")
    print("=" * 30)
    
    required_files = [
        'web/templates/tribal_lunar_wall.html',
        'web/static/css/tribal_lunar_wall.css',
        'web/static/js/tribal_lunar_wall.js'
    ]
    
    files_exist = 0
    
    for file_path in required_files:
        full_path = os.path.join('.', file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}: Exists")
            files_exist += 1
        else:
            print(f"❌ {file_path}: Missing")
    
    if files_exist == len(required_files):
        print(f"\n✅ All required files exist")
        return True
    else:
        print(f"\n⚠️ {files_exist}/{len(required_files)} required files exist")
        return False

def test_javascript_functionality():
    """Test JavaScript functionality by checking file content"""
    print("\n🔧 Testing JavaScript Functionality")
    print("=" * 40)
    
    try:
        js_file_path = 'web/static/js/tribal_lunar_wall.js'
        
        if not os.path.exists(js_file_path):
            print("❌ JavaScript file not found")
            return False
        
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for key JavaScript functions
        required_functions = [
            'initializeTribalLunarWall',
            'toggleNightMode',
            'navigateMonth',
            'updateCalendarDisplay',
            'showTorahScroll',
            'hideTorahScroll',
            'updateSabbathCountdown',
            'generateCalendarGrid',
            'updateTribalWatermark'
        ]
        
        functions_found = 0
        
        for func in required_functions:
            if func in js_content:
                print(f"✅ Function: {func}")
                functions_found += 1
            else:
                print(f"❌ Function: {func}")
        
        # Check for Hebrew months data
        if 'hebrewMonths' in js_content and 'Nisan' in js_content:
            print("✅ Hebrew months data: Present")
            functions_found += 1
        else:
            print("❌ Hebrew months data: Missing")
        
        # Check for tribal data
        if 'tribalData' in js_content and 'benjamin' in js_content:
            print("✅ Tribal data: Present")
            functions_found += 1
        else:
            print("❌ Tribal data: Missing")
        
        if functions_found >= len(required_functions) * 0.8:  # 80% threshold
            print(f"\n✅ JavaScript functionality: Good ({functions_found}/{len(required_functions) + 2} elements found)")
            return True
        else:
            print(f"\n⚠️ JavaScript functionality: Incomplete ({functions_found}/{len(required_functions) + 2} elements found)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing JavaScript functionality: {e}")
        return False

def test_css_styling():
    """Test CSS styling by checking file content"""
    print("\n🎨 Testing CSS Styling")
    print("=" * 25)
    
    try:
        css_file_path = 'web/static/css/tribal_lunar_wall.css'
        
        if not os.path.exists(css_file_path):
            print("❌ CSS file not found")
            return False
        
        with open(css_file_path, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # Check for key CSS classes and features
        required_elements = [
            '.lunar-wall-body',
            '.tribal-watermark',
            '.calendar-grid',
            '.sabbath',
            '.feast-day',
            '.new-moon',
            '.torah-scroll-popup',
            '.starfield',
            '.moving-moon',
            'night-mode',
            '--cyber-cyan',
            '--cyber-purple',
            '--onyx-black',
            'animation',
            '@keyframes'
        ]
        
        elements_found = 0
        
        for element in required_elements:
            if element in css_content:
                print(f"✅ CSS element: {element}")
                elements_found += 1
            else:
                print(f"❌ CSS element: {element}")
        
        if elements_found >= len(required_elements) * 0.8:  # 80% threshold
            print(f"\n✅ CSS styling: Good ({elements_found}/{len(required_elements)} elements found)")
            return True
        else:
            print(f"\n⚠️ CSS styling: Incomplete ({elements_found}/{len(required_elements)} elements found)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing CSS styling: {e}")
        return False

def test_html_template():
    """Test HTML template structure"""
    print("\n📄 Testing HTML Template")
    print("=" * 30)
    
    try:
        html_file_path = 'web/templates/tribal_lunar_wall.html'
        
        if not os.path.exists(html_file_path):
            print("❌ HTML template not found")
            return False
        
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for key HTML elements
        required_elements = [
            'TRIBAL LUNAR WALL',
            'id="nightModeToggle"',
            'id="tribeSelect"',
            'id="starfield"',
            'id="movingMoon"',
            'id="calendarGrid"',
            'id="torahScrollPopup"',
            'class="new-moon-display"',
            'class="upcoming-events"',
            'tribal-lunar-wall.css',
            'tribal-lunar-wall.js',
            'initializeTribalLunarWall'
        ]
        
        elements_found = 0
        
        for element in required_elements:
            if element in html_content:
                print(f"✅ HTML element: {element}")
                elements_found += 1
            else:
                print(f"❌ HTML element: {element}")
        
        if elements_found >= len(required_elements) * 0.8:  # 80% threshold
            print(f"\n✅ HTML template: Good ({elements_found}/{len(required_elements)} elements found)")
            return True
        else:
            print(f"\n⚠️ HTML template: Incomplete ({elements_found}/{len(required_elements)} elements found)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing HTML template: {e}")
        return False

if __name__ == "__main__":
    print("🌙 Testing Tribal Lunar Wall Implementation")
    print("=" * 60)
    
    # Test file structure
    files_ok = test_file_structure()
    
    # Test HTML template
    html_ok = test_html_template()
    
    # Test CSS styling
    css_ok = test_css_styling()
    
    # Test JavaScript functionality
    js_ok = test_javascript_functionality()
    
    # Test static files (requires server)
    static_ok = test_static_files()
    
    # Test route accessibility (requires server)
    route_ok = test_tribal_lunar_wall_route()
    
    print("\n📊 Test Results:")
    print("=" * 25)
    
    if files_ok:
        print("✅ File structure: Complete")
    else:
        print("❌ File structure: Issues found")
    
    if html_ok:
        print("✅ HTML template: Good")
    else:
        print("❌ HTML template: Issues found")
    
    if css_ok:
        print("✅ CSS styling: Good")
    else:
        print("❌ CSS styling: Issues found")
    
    if js_ok:
        print("✅ JavaScript functionality: Good")
    else:
        print("❌ JavaScript functionality: Issues found")
    
    if static_ok:
        print("✅ Static files: Accessible")
    else:
        print("❌ Static files: Issues found")
    
    if route_ok:
        print("✅ Route accessibility: Working")
    else:
        print("❌ Route accessibility: Issues found")
    
    overall_success = files_ok and html_ok and css_ok and js_ok and static_ok and route_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: Tribal Lunar Wall implementation is complete!")
        print("   - Dynamic calendar with 12-lunar cycle")
        print("   - New Moon phases with shofar icons")
        print("   - Weekly sabbaths highlighted in gold")
        print("   - Feast days with tribal banners")
        print("   - Torah scroll pop-ups with KJV verses")
        print("   - Tribal insignia integration")
        print("   - Night mode with starfield and moving moon")
        print("   - Responsive design with cyber-tribal aesthetic")
    else:
        print("\n⚠️ Some issues found - check details above")
    
    sys.exit(0 if overall_success else 1)
