#!/usr/bin/env python3
"""
Test Biblical Tokenomics API Fix
"""

import requests
import sys

def test_biblical_tokenomics_api():
    """Test the biblical tokenomics API endpoint"""
    print("🧪 Testing Biblical Tokenomics API Fix")
    print("=" * 50)
    
    try:
        # Test the API endpoint that was causing the datetime error
        response = requests.get('http://127.0.0.1:5000/api/biblical-tokenomics/status', timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API endpoint working successfully!")
            print(f"   Success: {data.get('success', False)}")
            print(f"   Current Yovel Cycle: {data.get('current_yovel_cycle', 'N/A')}")
            print(f"   Current Season: {data.get('current_season', 'N/A')}")
            print(f"   Is Sabbath Period: {data.get('is_sabbath_period', False)}")
            print(f"   Years Until Reset: {data.get('years_until_reset', 'N/A')}")
            
            # Check if gleaning pool data is present
            gleaning_pool = data.get('gleaning_pool', {})
            print(f"   Gleaning Pool Balance: {gleaning_pool.get('balance', 0)}")
            
            return True
        else:
            print(f"❌ API returned error status: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server")
        print("   Make sure the ONNYX server is running on http://127.0.0.1:5000")
        return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_other_tokenomics_endpoints():
    """Test other tokenomics-related endpoints"""
    print("\n🔍 Testing Other Tokenomics Endpoints")
    print("=" * 40)
    
    endpoints_to_test = [
        '/api/tokenomics/mining/status',
        '/api/mining/status'
    ]
    
    success_count = 0
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f'http://127.0.0.1:5000{endpoint}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint}: Working")
                success_count += 1
            else:
                print(f"❌ {endpoint}: Error {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    print(f"\nEndpoints working: {success_count}/{len(endpoints_to_test)}")
    return success_count == len(endpoints_to_test)

if __name__ == "__main__":
    print("🧪 Testing Biblical Tokenomics API Fixes")
    print("=" * 60)
    
    # Test the main API endpoint
    main_api_ok = test_biblical_tokenomics_api()
    
    # Test other related endpoints
    other_apis_ok = test_other_tokenomics_endpoints()
    
    print("\n📊 Final Results:")
    print("=" * 30)
    
    if main_api_ok:
        print("✅ Biblical Tokenomics API: FIXED")
    else:
        print("❌ Biblical Tokenomics API: STILL HAS ISSUES")
    
    if other_apis_ok:
        print("✅ Related APIs: Working")
    else:
        print("⚠️ Related APIs: Some issues")
    
    overall_success = main_api_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: Biblical Tokenomics datetime error is FIXED!")
        print("   - API endpoint now works without datetime import errors")
        print("   - Returns proper JSON response with tokenomics data")
    else:
        print("\n⚠️ Some issues remain - check details above")
    
    sys.exit(0 if overall_success else 1)
