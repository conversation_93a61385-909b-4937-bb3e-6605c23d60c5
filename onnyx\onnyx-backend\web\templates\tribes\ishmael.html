{% extends "base.html" %}

{% block title %}12 Princes of Ishmael - ONNYX Platform{% endblock %}

{% block head %}
<style>
.ishmael-container {
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.05), rgba(0, 255, 247, 0.05));
    min-height: 100vh;
    padding: 2rem 0;
}

.prince-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(154, 0, 255, 0.3);
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
}

.prince-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 35px 70px rgba(154, 0, 255, 0.4);
    border-color: var(--cyber-purple);
}

.prince-header {
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.2), rgba(154, 0, 255, 0.1));
    padding: 1.5rem;
    border-bottom: 1px solid rgba(154, 0, 255, 0.3);
}

.prince-body {
    padding: 1.5rem;
}

.biblical-quote {
    background: rgba(154, 0, 255, 0.1);
    border-left: 4px solid var(--cyber-purple);
    padding: 1rem;
    border-radius: 0.5rem;
    font-style: italic;
    margin: 1.5rem 0;
}

.prince-badge {
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-blue));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: bold;
    display: inline-block;
}

.witness-badge {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-green));
    color: var(--onyx-black);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-cyan));
    border-radius: 50%;
    opacity: 0.6;
    animation: float 5s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

.heritage-section {
    background: rgba(154, 0, 255, 0.1);
    border: 1px solid rgba(154, 0, 255, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="ishmael-container relative">
    <div class="floating-particles"></div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl mb-6">
                <span class="text-3xl">🏺</span>
            </div>
            <h1 class="text-4xl md:text-6xl font-orbitron font-bold text-white mb-4">12 Princes of Ishmael</h1>
            <p class="text-xl text-text-secondary max-w-4xl mx-auto mb-6">
                "And as for Ishmael, I have heard thee: Behold, I have blessed him, and will make him fruitful, and will multiply him exceedingly; twelve princes shall he beget, and I will make him a great nation" - Genesis 17:20 (KJV)
            </p>
            <div class="text-lg text-text-tertiary">
                The blessed sons of Abraham through Hagar, witnesses to the covenant promises
            </div>
        </div>

        <!-- Biblical Heritage -->
        <div class="heritage-section mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-6 text-center">Biblical Heritage</h2>
            <div class="biblical-quote">
                "And God heard the voice of the lad; and the angel of God called to Hagar out of heaven, and said unto her, Fear not; for God hath heard the voice of the lad where he is. Arise, lift up the lad, and hold him in thine hand; for I will make him a great nation" - Genesis 21:17-18 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-4xl mb-4">🤱</div>
                    <h3 class="text-xl font-semibold text-cyber-purple mb-2">Son of Abraham</h3>
                    <p class="text-text-secondary">Born to Abraham through Hagar, blessed by the Almighty with divine promises</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">🏹</div>
                    <h3 class="text-xl font-semibold text-cyber-cyan mb-2">Mighty Hunter</h3>
                    <p class="text-text-secondary">"And God was with the lad; and he grew, and dwelt in the wilderness, and became an archer" - Genesis 21:20</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">👑</div>
                    <h3 class="text-xl font-semibold text-cyber-green mb-2">Great Nation</h3>
                    <p class="text-text-secondary">Father of twelve princes, establishing a great nation as promised by God</p>
                </div>
            </div>
        </div>

        <!-- Covenant Role -->
        <div class="prince-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-6">Covenant Witness Role</h2>
            <div class="biblical-quote">
                "And he will be a wild man; his hand will be against every man, and every man's hand against him; and he shall dwell in the presence of all his brethren" - Genesis 16:12 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">Witness Nation Status</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-purple mr-3">•</span>
                            <span class="text-text-secondary">Immediate registration without Gate Keeper approval</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-purple mr-3">•</span>
                            <span class="text-text-secondary">Witnesses to covenant promises and blessings</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-purple mr-3">•</span>
                            <span class="text-text-secondary">Participants in biblical economic system</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-purple mr-3">•</span>
                            <span class="text-text-secondary">Recipients of gleaning pool provisions</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-4">Divine Blessings</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Blessed with fruitfulness and multiplication</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Established as a great nation by divine promise</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Dwelling in the presence of their brethren</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-cyber-green mr-3">•</span>
                            <span class="text-text-secondary">Inheritors of Abrahamic covenant blessings</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- The 12 Princes -->
        <div class="mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-8 text-center">The Twelve Princes</h2>
            <div class="biblical-quote">
                "And these are the names of the sons of Ishmael, by their names, according to their generations: the firstborn of Ishmael, Nebajoth; and Kedar, and Adbeel, and Mibsam, And Mishma, and Dumah, and Massa, Hadar, and Tema, Jetur, Naphish, and Kedemah: These are the sons of Ishmael, and these are their names, by their towns, and by their castles; twelve princes according to their nations" - Genesis 25:13-16 (KJV)
            </div>
            
            {% if ishmael_princes %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for prince in ishmael_princes %}
                <div class="prince-card">
                    <div class="prince-header">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-xl font-orbitron font-bold text-white">{{ prince.nation_name }}</h3>
                            {% if prince.flag_symbol %}
                            <span class="text-2xl">{{ prince.flag_symbol }}</span>
                            {% endif %}
                        </div>
                        <div class="prince-badge">Prince of Ishmael</div>
                    </div>
                    <div class="prince-body">
                        {% if prince.tribe_name %}
                        <p class="text-sm text-cyber-cyan mb-3">
                            <strong>Biblical Name:</strong> {{ prince.tribe_name }}
                        </p>
                        {% endif %}
                        {% if prince.description %}
                        <p class="text-text-secondary text-sm mb-4">{{ prince.description }}</p>
                        {% endif %}
                        {% if prince.historical_connection %}
                        <p class="text-text-tertiary text-xs mb-3">
                            <strong>Historical Connection:</strong> {{ prince.historical_connection }}
                        </p>
                        {% endif %}
                        <div class="text-center">
                            <div class="witness-badge">Witness Nation</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="prince-card p-8 text-center">
                <div class="text-4xl mb-4">📜</div>
                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">Princes Being Prepared</h3>
                <p class="text-text-secondary">The twelve princes of Ishmael are being prepared for registration in the covenant system. Check back soon for the complete listing.</p>
            </div>
            {% endif %}
        </div>

        <!-- Registration Information -->
        <div class="prince-card p-8 mb-12">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-6">Registration Process</h2>
            <div class="biblical-quote">
                "And the stranger that dwelleth with you shall be unto you as one born among you, and thou shalt love him as thyself; for ye were strangers in the land of Egypt: I am the LORD your God" - Leviticus 19:34 (KJV)
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">📝</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">1. Heritage Claim</h3>
                    <p class="text-sm text-text-secondary">Claim descent from one of the twelve princes of Ishmael</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-cyber-cyan rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🔍</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">2. Evidence Review</h3>
                    <p class="text-sm text-text-secondary">Submit genealogical and cultural evidence</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-green rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">3. Immediate Approval</h3>
                    <p class="text-sm text-text-secondary">No Gate Keeper voting required for witness nations</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-green to-cyber-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">✅</span>
                    </div>
                    <h3 class="text-lg font-orbitron font-bold text-cyber-green mb-2">4. Covenant Access</h3>
                    <p class="text-sm text-text-secondary">Full access to covenant economic system</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="prince-card p-8 text-center">
            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-6">Discover Your Heritage</h2>
            <div class="biblical-quote">
                "And I will bless them that bless thee, and curse him that curseth thee: and in thee shall all families of the earth be blessed" - Genesis 12:3 (KJV)
            </div>
            <p class="text-lg text-text-secondary mb-6">
                Are you descended from the twelve princes of Ishmael? Begin your journey of discovery 
                and join the covenant community as a witness nation.
            </p>
            <div class="flex justify-center space-x-4">
                <a href="{{ url_for('onboarding.eden_mode') }}" class="bg-gradient-to-r from-cyber-purple to-cyber-blue text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    Begin Eden Mode
                </a>
                <a href="{{ url_for('tribes.overview') }}" class="bg-gradient-to-r from-cyber-cyan to-cyber-green text-onyx-black font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:scale-105">
                    View All Nations
                </a>
            </div>
        </div>

        <!-- Navigation -->
        <div class="text-center mt-8">
            <div class="flex justify-center space-x-6">
                <a href="{{ url_for('tribes.israel') }}" class="text-cyber-cyan hover:text-cyan-400 transition-colors">
                    12 Tribes of Israel
                </a>
                <a href="{{ url_for('tribes.edom_dukes') }}" class="text-cyber-purple hover:text-purple-400 transition-colors">
                    12 Dukes of Edom
                </a>
                <a href="{{ url_for('home.index') }}" class="text-cyber-green hover:text-green-400 transition-colors">
                    Return Home
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function addFloatingParticles() {
    const container = document.querySelector('.floating-particles');
    if (container) {
        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particle.style.animationDuration = (4 + Math.random() * 2) + 's';
            container.appendChild(particle);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    addFloatingParticles();
});
</script>
{% endblock %}
