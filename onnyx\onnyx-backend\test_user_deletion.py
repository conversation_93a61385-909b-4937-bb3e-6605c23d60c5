#!/usr/bin/env python3
"""
Test User Deletion Functionality
"""

import requests
import sys

def test_user_deletion_api():
    """Test the user deletion API endpoint"""
    print("🗑️ Testing User Deletion API...")
    
    try:
        # Create session and login as genesis admin
        session = requests.Session()
        
        # Login
        login_data = {
            'email': '<EMAIL>',
            'password': 'Genesis2024!'
        }
        
        login_response = session.post(
            'http://127.0.0.1:5000/auth/login',
            data=login_data,
            timeout=10
        )
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print("✅ Logged in as genesis admin")
        
        # Test deletion endpoint with a non-existent user (should return 404)
        test_identity_id = "test_user_12345"
        
        delete_response = session.delete(
            f'http://127.0.0.1:5000/admin/api/users/{test_identity_id}',
            timeout=10
        )
        
        print(f"Delete response status: {delete_response.status_code}")
        print(f"Delete response content type: {delete_response.headers.get('content-type', 'unknown')}")
        
        if delete_response.status_code == 404:
            try:
                data = delete_response.json()
                if not data.get('success') and 'not found' in data.get('error', '').lower():
                    print("✅ Delete API returns proper JSON for non-existent user")
                    return True
                else:
                    print(f"❌ Unexpected JSON response: {data}")
                    return False
            except Exception as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Response text: {delete_response.text[:200]}")
                return False
        else:
            print(f"❌ Unexpected status code: {delete_response.status_code}")
            print(f"Response text: {delete_response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing deletion API: {e}")
        return False

def test_admin_protection():
    """Test that system admins cannot be deleted"""
    print("\n🛡️ Testing Admin Protection...")
    
    try:
        # Create session and login as genesis admin
        session = requests.Session()
        
        # Login
        login_data = {
            'email': '<EMAIL>',
            'password': 'Genesis2024!'
        }
        
        login_response = session.post(
            'http://127.0.0.1:5000/auth/login',
            data=login_data,
            timeout=10
        )
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        # Try to delete the genesis admin (should be protected)
        genesis_identity_id = "ONX4d0fb6bb0dda45a7"
        
        delete_response = session.delete(
            f'http://127.0.0.1:5000/admin/api/users/{genesis_identity_id}',
            timeout=10
        )
        
        print(f"Self-deletion response status: {delete_response.status_code}")
        
        if delete_response.status_code == 403:
            try:
                data = delete_response.json()
                if not data.get('success') and 'cannot delete' in data.get('error', '').lower():
                    print("✅ System admin deletion properly blocked")
                    return True
                else:
                    print(f"❌ Unexpected protection response: {data}")
                    return False
            except Exception as e:
                print(f"❌ Failed to parse protection response: {e}")
                return False
        else:
            print(f"❌ Admin protection failed - status: {delete_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing admin protection: {e}")
        return False

def test_endpoint_accessibility():
    """Test that the deletion endpoint is accessible"""
    print("\n🔌 Testing Endpoint Accessibility...")

    try:
        # Test without authentication (should require auth)
        headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
        response = requests.delete('http://127.0.0.1:5000/admin/api/users/test', headers=headers, timeout=5)

        print(f"Unauthenticated request status: {response.status_code}")

        if response.status_code in [401, 403, 302]:
            print("✅ Endpoint properly requires authentication")
            return True
        else:
            print(f"❌ Endpoint security issue - status: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False

    except Exception as e:
        print(f"❌ Error testing endpoint accessibility: {e}")
        return False

def check_database_structure():
    """Check if the database has the required tables for deletion"""
    print("\n🗄️ Checking Database Structure...")
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))
        
        from shared.db.db import db
        
        # Check required tables exist
        required_tables = [
            'identities', 'identity_passwords', 'secure_sessions',
            'token_balances', 'selas', 'mining_rewards', 'deeds_ledger',
            'loans', 'mikvah_transactions', 'etzem_scores'
        ]
        
        existing_tables = []
        for table in required_tables:
            try:
                result = db.query_one(f"SELECT COUNT(*) as count FROM {table}")
                existing_tables.append(table)
                print(f"  ✅ {table}: {result['count']} records")
            except Exception as e:
                print(f"  ❌ {table}: {e}")
        
        if len(existing_tables) >= len(required_tables) * 0.8:
            print("✅ Database structure sufficient for user deletion")
            return True
        else:
            print("❌ Missing critical database tables")
            return False
            
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing User Deletion Functionality")
    print("=" * 60)
    
    # Check database structure first
    db_ok = check_database_structure()
    
    # Test endpoint accessibility
    endpoint_ok = test_endpoint_accessibility()
    
    # Test API functionality
    api_ok = test_user_deletion_api()
    
    # Test admin protection
    protection_ok = test_admin_protection()
    
    print("\n📊 Final Results:")
    print("=" * 30)
    
    if db_ok:
        print("✅ Database structure: OK")
    else:
        print("❌ Database structure: ISSUES")
    
    if endpoint_ok:
        print("✅ Endpoint security: OK")
    else:
        print("❌ Endpoint security: ISSUES")
    
    if api_ok:
        print("✅ API functionality: OK")
    else:
        print("❌ API functionality: ISSUES")
    
    if protection_ok:
        print("✅ Admin protection: OK")
    else:
        print("❌ Admin protection: ISSUES")
    
    overall_success = db_ok and endpoint_ok and api_ok and protection_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: User deletion functionality is working!")
        print("   - API endpoint returns proper JSON responses")
        print("   - System administrators are protected from deletion")
        print("   - Authentication is properly required")
        print("   - Database structure supports safe deletion")
    else:
        print("\n⚠️ Some issues found - check details above")
    
    sys.exit(0 if overall_success else 1)
