#!/usr/bin/env python3
"""
Migrate Biblical Loans System
Add support for biblical interest rules and tribal affiliation-based lending
"""

import sqlite3
import sys
import os

def migrate_loans_table():
    """Add new columns to loans table for biblical compliance"""
    
    print("🔧 Migrating Loans Table for Biblical Compliance")
    print("=" * 55)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current table structure
        cursor.execute('PRAGMA table_info(loans)')
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"Current loans table columns: {column_names}")
        
        # Add new columns for biblical compliance
        new_columns = [
            ('interest_rate', 'REAL DEFAULT 0.0'),
            ('loan_type', 'TEXT DEFAULT "standard"'),
            ('biblical_rule', 'TEXT DEFAULT ""'),
            ('lender_tribal_affiliation', 'TEXT DEFAULT "UNKNOWN"'),
            ('borrower_tribal_affiliation', 'TEXT DEFAULT "UNKNOWN"'),
            ('updated_at', 'INTEGER DEFAULT 0')
        ]
        
        added_columns = 0
        for column_name, column_def in new_columns:
            if column_name not in column_names:
                print(f"Adding column: {column_name}")
                cursor.execute(f'ALTER TABLE loans ADD COLUMN {column_name} {column_def}')
                added_columns += 1
                print(f"✅ Added {column_name} column")
            else:
                print(f"✅ {column_name} column already exists")
        
        if added_columns > 0:
            conn.commit()
            print(f"✅ Added {added_columns} new columns to loans table")
        else:
            print("✅ All columns already exist")
        
        # Verify new table structure
        cursor.execute('PRAGMA table_info(loans)')
        new_columns_info = cursor.fetchall()
        print(f"\nUpdated loans table has {len(new_columns_info)} columns:")
        for col in new_columns_info:
            print(f"   - {col[1]} {col[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error migrating loans table: {e}")
        return False

def update_existing_loans():
    """Update existing loans with biblical compliance data"""
    
    print("\n🔄 Updating Existing Loans with Biblical Data")
    print("=" * 50)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all existing loans
        cursor.execute("SELECT loan_id, lender_id, borrower_id FROM loans")
        existing_loans = cursor.fetchall()
        
        if not existing_loans:
            print("ℹ️ No existing loans to update")
            conn.close()
            return True
        
        print(f"Found {len(existing_loans)} existing loans to update")
        
        updated_count = 0
        for loan_id, lender_id, borrower_id in existing_loans:
            # Get tribal affiliations
            lender_affiliation = get_tribal_affiliation(cursor, lender_id)
            borrower_affiliation = get_tribal_affiliation(cursor, borrower_id)
            
            # Determine loan type and biblical rule
            israelite_codes = ['JU', 'BE', 'LE', 'SI', 'EP', 'MA', 'IS', 'ZE', 'NA', 'GA', 'AS', 'RE', 'ISR']
            lender_is_israelite = lender_affiliation in israelite_codes
            borrower_is_israelite = borrower_affiliation in israelite_codes
            
            if lender_is_israelite and borrower_is_israelite:
                loan_type = "covenant_mutual_aid"
                biblical_rule = "Exodus 22:25 - No usury among covenant brothers"
                interest_rate = 0.0
            elif lender_is_israelite and not borrower_is_israelite:
                loan_type = "covenant_to_witness"
                biblical_rule = "Deuteronomy 23:20 - May charge interest to strangers"
                interest_rate = 0.0  # Default to mercy
            elif not lender_is_israelite and borrower_is_israelite:
                loan_type = "witness_to_covenant"
                biblical_rule = "Market terms - Witness nation lending"
                interest_rate = 0.0  # Default to mercy
            else:
                loan_type = "witness_to_witness"
                biblical_rule = "Standard market practice"
                interest_rate = 0.0  # Default to mercy
            
            # Update the loan
            cursor.execute("""
                UPDATE loans 
                SET interest_rate = ?, loan_type = ?, biblical_rule = ?,
                    lender_tribal_affiliation = ?, borrower_tribal_affiliation = ?,
                    updated_at = strftime('%s', 'now')
                WHERE loan_id = ?
            """, (interest_rate, loan_type, biblical_rule, 
                  lender_affiliation, borrower_affiliation, loan_id))
            
            updated_count += 1
            print(f"   ✅ Updated {loan_id}: {loan_type} ({lender_affiliation} → {borrower_affiliation})")
        
        conn.commit()
        print(f"\n✅ Updated {updated_count} existing loans with biblical compliance data")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating existing loans: {e}")
        return False

def get_tribal_affiliation(cursor, identity_id):
    """Get tribal affiliation for an identity"""
    try:
        cursor.execute("""
            SELECT nation_code, nation_of_origin 
            FROM identities 
            WHERE identity_id = ?
        """, (identity_id,))
        
        result = cursor.fetchone()
        if result:
            return result[0] or result[1] or 'UNKNOWN'
        return 'UNKNOWN'
        
    except Exception:
        return 'UNKNOWN'

def test_biblical_loans_system():
    """Test the biblical loans system"""
    
    print("\n🧪 Testing Biblical Loans System")
    print("=" * 40)
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test loan queries
        cursor.execute("SELECT COUNT(*) FROM loans")
        total_loans = cursor.fetchone()[0]
        print(f"✅ Total loans in system: {total_loans}")
        
        # Test by loan type
        cursor.execute("SELECT loan_type, COUNT(*) FROM loans GROUP BY loan_type")
        loan_types = cursor.fetchall()
        print("✅ Loans by type:")
        for loan_type, count in loan_types:
            print(f"   - {loan_type}: {count}")
        
        # Test biblical rules
        cursor.execute("SELECT DISTINCT biblical_rule FROM loans")
        biblical_rules = cursor.fetchall()
        print("✅ Biblical rules in use:")
        for rule in biblical_rules:
            if rule[0]:
                print(f"   - {rule[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing biblical loans system: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Biblical Loans System Migration")
    print("=" * 60)
    
    # Migrate table structure
    table_migrated = migrate_loans_table()
    
    # Update existing loans
    loans_updated = update_existing_loans()
    
    # Test the system
    system_tested = test_biblical_loans_system()
    
    print("\n📊 Migration Results:")
    print("=" * 30)
    
    if table_migrated:
        print("✅ Table migration: SUCCESS")
    else:
        print("❌ Table migration: FAILED")
    
    if loans_updated:
        print("✅ Loan updates: SUCCESS")
    else:
        print("❌ Loan updates: FAILED")
    
    if system_tested:
        print("✅ System testing: SUCCESS")
    else:
        print("❌ System testing: FAILED")
    
    overall_success = table_migrated and loans_updated and system_tested
    
    if overall_success:
        print("\n🎉 SUCCESS: Biblical loans system is ready!")
        print("   - Database schema updated")
        print("   - Existing loans migrated")
        print("   - Biblical interest rules implemented")
        print("   - Tribal affiliation-based lending active")
    else:
        print("\n⚠️ Some issues found - check details above")
    
    sys.exit(0 if overall_success else 1)
