#!/usr/bin/env python3
"""
Fix Genesis Admin Password with Proper PBKDF2 Hashing
"""

import sys
import os
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from shared.db.db import db
from web.secure_auth import SecureAuth

def fix_genesis_password():
    """Fix the genesis admin password using the proper SecureAuth system"""
    
    # Password to set
    new_password = "Genesis2024!"
    identity_id = "ONX4d0fb6bb0dda45a7"
    
    print("🔐 Fixing Genesis Admin Password with Proper PBKDF2 Hashing...")
    print(f"Identity ID: {identity_id}")
    print(f"New Password: {new_password}")
    
    try:
        # Check if account exists
        identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))
        if not identity:
            print("❌ Genesis admin account not found")
            return False
        
        print(f"✅ Found account: {identity['name']} ({identity['email']})")
        
        # Use SecureAuth to set the password properly
        success = SecureAuth.set_password(identity_id, new_password)
        
        if success:
            print("✅ Password set using SecureAuth system")
        else:
            print("❌ Failed to set password using SecureAuth")
            return False
        
        # Verify the password was set correctly
        password_record = db.query_one("""
            SELECT password_hash, salt FROM identity_passwords
            WHERE identity_id = ?
        """, (identity_id,))
        
        if password_record:
            print("✅ Password record found in database")
            print(f"   Hash length: {len(password_record['password_hash'])}")
            print(f"   Salt length: {len(password_record['salt'])}")
            
            # Test password verification
            if SecureAuth.verify_password(new_password, password_record['password_hash'], password_record['salt']):
                print("✅ Password verification successful")
            else:
                print("❌ Password verification failed")
                return False
        else:
            print("❌ No password record found after setting")
            return False
        
        print("\n🎉 Password Fixed Successfully!")
        print("\n📋 Login Credentials:")
        print(f"   Email: {identity['email']}")
        print(f"   Password: {new_password}")
        print("\n🌐 Login URL:")
        print("   http://127.0.0.1:5000/auth/login")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing password: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_simulation():
    """Simulate the login process to verify it works"""
    print("\n🧪 Testing Login Simulation...")
    
    try:
        email = "<EMAIL>"
        password = "Genesis2024!"
        
        # Find identity by email (same as login process)
        identity = db.query_one("SELECT * FROM identities WHERE email = ?", (email,))
        
        if not identity:
            print("❌ Identity not found")
            return False
        
        print(f"✅ Identity found: {identity['name']}")
        
        # Get password record (same as login process)
        password_record = db.query_one("""
            SELECT password_hash, salt FROM identity_passwords
            WHERE identity_id = ?
        """, (identity['identity_id'],))
        
        if not password_record:
            print("❌ No password record found")
            return False
        
        print("✅ Password record found")
        
        # Verify password (same as login process)
        if SecureAuth.verify_password(password, password_record['password_hash'], password_record['salt']):
            print("✅ Password verification successful - login should work!")
            return True
        else:
            print("❌ Password verification failed - login will fail")
            return False
            
    except Exception as e:
        print(f"❌ Login simulation error: {e}")
        return False

def check_admin_permissions():
    """Check that the genesis admin has proper permissions"""
    print("\n🔑 Checking Admin Permissions...")
    
    try:
        identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", ("ONX4d0fb6bb0dda45a7",))
        
        if identity:
            print(f"✅ Role: {identity.get('role', 'N/A')}")
            print(f"✅ Role Class: {identity.get('role_class', 'N/A')}")
            print(f"✅ Verification Level: {identity.get('verification_level', 'N/A')}")
            print(f"✅ Status: {identity.get('status', 'N/A')}")
            
            # Check if it's a system admin
            if identity.get('role_class') == 'system_admin' or identity.get('role') == 'system_admin':
                print("✅ Has system admin privileges")
                return True
            else:
                print("⚠️ May not have full admin privileges")
                return False
        else:
            print("❌ Identity not found")
            return False
            
    except Exception as e:
        print(f"❌ Permission check error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ONNYX Genesis Password Fix")
    print("=" * 50)
    
    # Fix the password
    password_success = fix_genesis_password()
    
    if password_success:
        # Test login simulation
        login_success = test_login_simulation()
        
        # Check permissions
        permissions_ok = check_admin_permissions()
        
        if login_success and permissions_ok:
            print("\n🎉 SUCCESS: Genesis admin is ready!")
            print("\n📋 Next Steps:")
            print("1. Go to: http://127.0.0.1:5000/auth/login")
            print("2. Email: <EMAIL>")
            print("3. Password: Genesis2024!")
            print("4. Access onboarding dashboard: http://127.0.0.1:5000/onboarding/")
        else:
            print("\n⚠️ Password fixed but some issues remain")
    else:
        print("\n❌ Password fix failed")
