"""
Admin Panel Routes

Administrative interface for system management and oversight.
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from web.auth_decorators import require_admin, require_auth, get_current_user
from shared.db.db import db
import json
import time
import logging

logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def log_security_event(identity_id, event_type, description, metadata=None):
    """Log security events for audit purposes"""
    try:
        db.execute("""
            INSERT INTO security_audit_log (identity_id, event_type, description, metadata, timestamp)
            VALUES (?, ?, ?, ?, ?)
        """, (identity_id, event_type, description, json.dumps(metadata or {}), int(time.time())))
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@require_admin
def dashboard():
    """Admin dashboard overview"""
    try:
        current_user = get_current_user()
        
        # Get system statistics
        stats = {
            'total_identities': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
            'active_identities': db.query_one("SELECT COUNT(*) as count FROM identities WHERE status = 'active'")['count'],
            'pending_verification': db.query_one("SELECT COUNT(*) as count FROM identities WHERE verification_level LIKE '%Pending%'")['count'],
            'total_selas': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
            'total_tokens': db.query_one("SELECT COUNT(*) as count FROM tokens")['count'],
            'total_pools': db.query_one("SELECT COUNT(*) as count FROM jubilee_pools")['count'],
            'total_deeds': db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")['count'],
            'system_admins': db.query_one("SELECT COUNT(*) as count FROM identities WHERE role_class = 'system_admin'")['count']
        }
        
        # Get recent activities
        recent_identities = db.query("""
            SELECT identity_id, name, created_at, verification_level, status
            FROM identities 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        recent_deeds = db.query("""
            SELECT d.deed_id, d.deed_type, d.description, d.timestamp, i.name as identity_name
            FROM deeds_ledger d
            JOIN identities i ON d.identity_id = i.identity_id
            ORDER BY d.timestamp DESC
            LIMIT 10
        """)
        
        return render_template('admin/dashboard.html',
                             stats=stats,
                             recent_identities=recent_identities,
                             recent_deeds=recent_deeds,
                             current_user=current_user)
        
    except Exception as e:
        logger.error(f"Admin dashboard error: {e}")
        flash('Error loading admin dashboard', 'error')
        return redirect(url_for('dashboard.overview'))

@admin_bp.route('/users')
@require_admin
def user_management():
    """User management interface"""
    try:
        # Get all users with pagination
        page = request.args.get('page', 1, type=int)
        per_page = 20
        offset = (page - 1) * per_page
        
        users = db.query("""
            SELECT identity_id, name, email, verification_level, status, role_class, created_at
            FROM identities 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        """, (per_page, offset))
        
        total_users = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
        
        return render_template('admin/users.html',
                             users=users,
                             page=page,
                             per_page=per_page,
                             total_users=total_users)
        
    except Exception as e:
        logger.error(f"User management error: {e}")
        flash('Error loading user management', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/system')
@require_admin
def system_management():
    """System configuration and management"""
    try:
        # Get system configuration
        system_info = {
            'database_tables': len(db.query("SELECT name FROM sqlite_master WHERE type='table'")),
            'total_records': sum([
                db.query_one(f"SELECT COUNT(*) as count FROM {table}")['count']
                for table in ['identities', 'selas', 'tokens', 'jubilee_pools', 'deeds_ledger']
            ]),
            'last_backup': 'Not configured',  # TODO: Implement backup system
            'system_version': '1.0.0-beta',
            'uptime': 'Unknown'  # TODO: Track system uptime
        }
        
        return render_template('admin/system.html',
                             system_info=system_info)
        
    except Exception as e:
        logger.error(f"System management error: {e}")
        flash('Error loading system management', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/user/<identity_id>/update', methods=['POST'])
@require_admin
def update_user(identity_id):
    """Update user information"""
    try:
        data = request.get_json()
        
        # Validate input
        allowed_fields = ['verification_level', 'status', 'role_class']
        updates = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not updates:
            return jsonify({'error': 'No valid fields to update'}), 400
        
        # Build update query
        set_clause = ', '.join([f"{k} = ?" for k in updates.keys()])
        values = list(updates.values()) + [identity_id]
        
        db.execute(f"UPDATE identities SET {set_clause}, updated_at = ? WHERE identity_id = ?", 
                  values + [int(time.time())])
        
        # Log the admin action
        current_user = get_current_user()
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_UPDATE_{identity_id}_{int(time.time())}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Updated user {identity_id}",
            int(time.time()),
            json.dumps({'action': 'user_update', 'target': identity_id, 'changes': updates})
        ))
        
        return jsonify({'success': True, 'message': 'User updated successfully'})
        
    except Exception as e:
        logger.error(f"User update error: {e}")
        return jsonify({'error': 'Failed to update user'}), 500

@admin_bp.route('/api/users/<identity_id>', methods=['DELETE'])
@require_admin
def delete_user(identity_id):
    """Delete a user and all associated data"""
    try:
        current_user = get_current_user()

        # Prevent self-deletion
        if identity_id == current_user['identity_id']:
            return jsonify({'success': False, 'error': 'Cannot delete your own account'}), 403

        # Get user info before deletion for logging
        user_info = db.query_one("SELECT name, email, role_class FROM identities WHERE identity_id = ?", (identity_id,))
        if not user_info:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Prevent deletion of other system admins (safety measure)
        if user_info['role_class'] == 'system_admin':
            return jsonify({'success': False, 'error': 'Cannot delete system administrators'}), 403

        # Begin transaction to delete all related data
        try:
            # Delete from related tables first (foreign key constraints)
            tables_to_clean = [
                'identity_passwords',
                'token_balances',
                'deeds_ledger',
                'mining_rewards',
                'labor_records',
                'sela_relationships',
                'secure_sessions',
                'auth_attempts'
            ]

            for table in tables_to_clean:
                try:
                    db.execute(f"DELETE FROM {table} WHERE identity_id = ?", (identity_id,))
                except Exception as e:
                    logger.warning(f"Could not delete from {table}: {e}")

            # Delete from selas table if user owns any
            db.execute("DELETE FROM selas WHERE identity_id = ?", (identity_id,))

            # Finally delete the identity
            db.execute("DELETE FROM identities WHERE identity_id = ?", (identity_id,))

            # Log the admin action
            db.execute("""
                INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                f"ADMIN_DELETE_{identity_id}_{int(time.time())}",
                current_user['identity_id'],
                'ADMIN_ACTION',
                f"Deleted user {user_info['name']} ({user_info['email']})",
                int(time.time()),
                json.dumps({
                    'action': 'user_deletion',
                    'target': identity_id,
                    'target_name': user_info['name'],
                    'target_email': user_info['email'],
                    'target_role': user_info['role_class']
                })
            ))

            logger.info(f"Admin {current_user['email']} deleted user {user_info['email']} ({identity_id})")
            return jsonify({
                'success': True,
                'message': f"User {user_info['name']} has been permanently deleted"
            })

        except Exception as e:
            logger.error(f"Error during user deletion transaction: {e}")
            return jsonify({'error': 'Failed to delete user completely'}), 500

    except Exception as e:
        logger.error(f"User deletion error: {e}")
        return jsonify({'error': 'Failed to delete user'}), 500

@admin_bp.route('/api/user/<identity_id>/details')
@require_admin
def get_user_details(identity_id):
    """Get detailed user information"""
    try:
        # Get user details
        user = db.query_one("""
            SELECT identity_id, name, email, role_class, tribal_affiliation,
                   status, verification_level, created_at, updated_at, etzem_score,
                   nation_code, nation_name, covenant_accepted, vault_status
            FROM identities
            WHERE identity_id = ?
        """, (identity_id,))

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get user's token balances
        balances = db.query("""
            SELECT token_id, balance, last_updated
            FROM token_balances
            WHERE identity_id = ?
        """, (identity_id,))

        # Get user's recent deeds
        deeds = db.query("""
            SELECT deed_type, description, timestamp, deed_value
            FROM deeds_ledger
            WHERE identity_id = ?
            ORDER BY timestamp DESC
            LIMIT 10
        """, (identity_id,))

        # Get user's selas
        selas = db.query("""
            SELECT sela_id, name, status, mining_tier, mining_power
            FROM selas
            WHERE identity_id = ?
        """, (identity_id,))

        return jsonify({
            'user': dict(user),
            'balances': [dict(b) for b in balances],
            'recent_deeds': [dict(d) for d in deeds],
            'selas': [dict(s) for s in selas]
        })

    except Exception as e:
        logger.error(f"Error getting user details: {e}")
        return jsonify({'error': 'Failed to get user details'}), 500

@admin_bp.route('/api/system/stats')
@require_admin
def system_stats():
    """Get real-time system statistics"""
    try:
        stats = {
            'identities': {
                'total': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
                'active': db.query_one("SELECT COUNT(*) as count FROM identities WHERE status = 'active'")['count'],
                'pending': db.query_one("SELECT COUNT(*) as count FROM identities WHERE verification_level LIKE '%Pending%'")['count']
            },
            'selas': {
                'total': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
                'active': db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count']
            },
            'tokenomics': {
                'pools': db.query_one("SELECT COUNT(*) as count FROM jubilee_pools")['count'],
                'deeds': db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")['count'],
                'tokens': db.query_one("SELECT COUNT(*) as count FROM tokens")['count']
            }
        }
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"System stats error: {e}")
        return jsonify({'error': 'Failed to get system stats'}), 500

@admin_bp.route('/api/validators/<sela_id>', methods=['DELETE'])
@require_admin
def delete_validator(sela_id):
    """Delete a validator (Sela) and all associated data. REQUIRES ADMIN PERMISSION."""
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        # Get validator details for logging
        validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
        if not validator:
            return jsonify({'success': False, 'error': 'Validator not found'}), 404

        # Begin transaction for safe deletion
        db.execute("BEGIN TRANSACTION")

        try:
            # Delete associated mining rewards
            db.execute("DELETE FROM mining_rewards WHERE identity_id = ?", (validator['identity_id'],))

            # Delete the Sela itself
            db.execute("DELETE FROM selas WHERE sela_id = ?", (sela_id,))

            # Commit transaction
            db.execute("COMMIT")

            # Log the deletion
            log_security_event(
                current_user['identity_id'],
                'VALIDATOR_DELETION',
                f"Deleted validator {validator['name']} (ID: {sela_id})",
                {'validator_name': validator['name'], 'sela_id': sela_id}
            )

            logger.info(f"Admin {current_user['name']} deleted validator {validator['name']} (ID: {sela_id})")

            return jsonify({
                'success': True,
                'message': f"Validator {validator['name']} successfully deleted"
            })

        except Exception as e:
            # Rollback on error
            db.execute("ROLLBACK")
            raise e

    except Exception as e:
        logger.error(f"Error deleting validator {sela_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


