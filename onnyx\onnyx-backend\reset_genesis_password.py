#!/usr/bin/env python3
"""
Reset Genesis Account Password
Allows resetting the <NAME_EMAIL> account
"""

import os
import sys
import sqlite3
import getpass
import hashlib
import secrets
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

def hash_password(password: str, salt: str = None) -> tuple:
    """Hash password with salt using PBKDF2"""
    if salt is None:
        salt = secrets.token_hex(32)
    
    # Use PBKDF2 with SHA-256, 100,000 iterations
    password_hash = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000
    )
    
    return password_hash.hex(), salt

def reset_genesis_password():
    """Reset the <NAME_EMAIL>"""
    
    print("🔐 ONNYX Genesis Account Password Reset")
    print("=" * 50)
    
    # Connect to database
    db_path = "shared/db/db/onnyx.db"
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if genesis user exists
        cursor.execute('SELECT identity_id, email, role_class FROM identities WHERE email=?', ('<EMAIL>',))
        genesis_user = cursor.fetchone()
        
        if not genesis_user:
            print("❌ Genesis user (<EMAIL>) not found in database")
            return False
        
        identity_id, email, role_class = genesis_user
        print(f"✅ Found genesis user: {email} ({role_class})")
        print(f"   Identity ID: {identity_id}")
        
        # Get new password
        print("\n🔑 Enter new password for genesis account:")
        new_password = getpass.getpass("New password: ")
        
        if len(new_password) < 8:
            print("❌ Password must be at least 8 characters long")
            return False
        
        confirm_password = getpass.getpass("Confirm password: ")
        
        if new_password != confirm_password:
            print("❌ Passwords do not match")
            return False
        
        # Hash the password
        password_hash, salt = hash_password(new_password)
        
        # Update password in database
        cursor.execute("""
            INSERT OR REPLACE INTO identity_passwords
            (identity_id, password_hash, salt, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """, (identity_id, password_hash, salt, int(time.time()), int(time.time())))
        
        conn.commit()
        conn.close()
        
        print("\n✅ Password reset successfully!")
        print(f"   Account: {email}")
        print(f"   You can now login with the new password")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting password: {e}")
        return False

def show_genesis_info():
    """Show information about the genesis account"""
    
    print("📋 Genesis Account Information")
    print("-" * 30)
    
    db_path = "shared/db/db/onnyx.db"
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)y
        cursor = conn.cursor()
        
        # Get genesis user info
        cursor.execute('''
            SELECT identity_id, name, email, role_class, tribal_affiliation, 
                   status, created_at, updated_at
            FROM identities 
            WHERE email = ?
        ''', ('<EMAIL>',))
        
        genesis_info = cursor.fetchone()
        
        if genesis_info:
            identity_id, name, email, role_class, tribal_affiliation, status, created_at, updated_at = genesis_info
            
            print(f"Identity ID: {identity_id}")
            print(f"Name: {name}")
            print(f"Email: {email}")
            print(f"Role: {role_class}")
            print(f"Tribal Affiliation: {tribal_affiliation or 'None'}")
            print(f"Status: {status}")
            print(f"Created: {time.ctime(created_at) if created_at else 'Unknown'}")
            
            # Check if password exists
            cursor.execute('SELECT COUNT(*) FROM identity_passwords WHERE identity_id = ?', (identity_id,))
            has_password = cursor.fetchone()[0] > 0
            print(f"Has Password: {'Yes' if has_password else 'No'}")
            
        else:
            print("❌ Genesis account not found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error retrieving genesis info: {e}")

if __name__ == "__main__":
    print("🛡️ ONNYX Genesis Account Management")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "info":
        show_genesis_info()
    else:
        print("This script will reset the <NAME_EMAIL>")
        print("Make sure you have access to the ONNYX database.")
        print()
        
        confirm = input("Do you want to proceed? (y/N): ").lower().strip()
        if confirm == 'y':
            success = reset_genesis_password()
            if success:
                print("\n🎉 Password reset completed successfully!")
            else:
                print("\n💥 Password reset failed!")
                sys.exit(1)
        else:
            print("Operation cancelled.")
