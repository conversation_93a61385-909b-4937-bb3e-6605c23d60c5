#!/usr/bin/env python3
"""
Fix Genesis Account Role
<NAME_EMAIL> account to have proper system_admin role
"""

import os
import sys
import sqlite3
import time
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

def fix_genesis_role():
    """Fix the <NAME_EMAIL> to system_admin"""
    
    print("🔧 ONNYX Genesis Account Role Fix")
    print("=" * 50)
    
    # Connect to database
    db_path = "shared/db/db/onnyx.db"
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current genesis user
        cursor.execute('SELECT identity_id, email, role_class, metadata FROM identities WHERE email=?', ('<EMAIL>',))
        genesis_user = cursor.fetchone()
        
        if not genesis_user:
            print("❌ Genesis user (<EMAIL>) not found in database")
            return False
        
        identity_id, email, current_role, metadata_str = genesis_user
        print(f"✅ Found genesis user: {email}")
        print(f"   Current Role: {current_role}")
        print(f"   Identity ID: {identity_id}")
        
        # Parse existing metadata
        try:
            metadata = json.loads(metadata_str) if metadata_str else {}
        except:
            metadata = {}
        
        # Update metadata with admin privileges
        metadata.update({
            "genesis_identity": True,
            "platform_founder": True,
            "admin_privileges": True,
            "system_admin": True,
            "role_updated": True,
            "role_update_timestamp": int(time.time())
        })
        
        # Update the user role and metadata
        cursor.execute("""
            UPDATE identities 
            SET role_class = ?, metadata = ?, updated_at = ?
            WHERE identity_id = ?
        """, ('system_admin', json.dumps(metadata), int(time.time()), identity_id))
        
        conn.commit()
        
        # Verify the update
        cursor.execute('SELECT role_class FROM identities WHERE identity_id=?', (identity_id,))
        new_role = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"\n✅ Role updated successfully!")
        print(f"   Old Role: {current_role}")
        print(f"   New Role: {new_role}")
        print(f"   Admin Privileges: Enabled")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing genesis role: {e}")
        return False

def show_current_admins():
    """Show all current system administrators"""
    
    print("👑 Current System Administrators")
    print("-" * 30)
    
    db_path = "shared/db/db/onnyx.db"
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all system admins
        cursor.execute('''
            SELECT identity_id, name, email, role_class, status, created_at
            FROM identities 
            WHERE role_class = 'system_admin' OR metadata LIKE '%admin_privileges%'
            ORDER BY created_at
        ''')
        
        admins = cursor.fetchall()
        
        if admins:
            for admin in admins:
                identity_id, name, email, role_class, status, created_at = admin
                print(f"📧 {email}")
                print(f"   Name: {name}")
                print(f"   Role: {role_class}")
                print(f"   Status: {status}")
                print(f"   Created: {time.ctime(created_at) if created_at else 'Unknown'}")
                print()
        else:
            print("❌ No system administrators found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error retrieving admin info: {e}")

if __name__ == "__main__":
    print("🛡️ ONNYX Genesis Account Role Management")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "admins":
        show_current_admins()
    else:
        print("This script <NAME_EMAIL> to have system_admin role")
        print("This will grant full administrative privileges to the genesis account.")
        print()
        
        confirm = input("Do you want to proceed? (y/N): ").lower().strip()
        if confirm == 'y':
            success = fix_genesis_role()
            if success:
                print("\n🎉 Genesis role fix completed successfully!")
                print("The genesis account now has full system administrator privileges.")
            else:
                print("\n💥 Genesis role fix failed!")
                sys.exit(1)
        else:
            print("Operation cancelled.")
