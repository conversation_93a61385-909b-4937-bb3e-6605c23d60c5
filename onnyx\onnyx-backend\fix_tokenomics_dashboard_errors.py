#!/usr/bin/env python3
"""
Fix Biblical Tokenomics Dashboard Database Errors
"""

import sqlite3
import sys
import os

def fix_deeds_ledger_table():
    """Fix the deeds_ledger table by adding missing deed_value column"""
    
    print("🔧 Fixing deeds_ledger table...")
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if deeds_ledger table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='deeds_ledger'")
        if not cursor.fetchone():
            print("❌ deeds_ledger table does not exist")
            conn.close()
            return False
        
        # Check current table structure
        cursor.execute('PRAGMA table_info(deeds_ledger)')
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"Current deeds_ledger columns: {column_names}")
        
        # Add deed_value column if missing
        if 'deed_value' not in column_names:
            print("Adding missing deed_value column...")
            cursor.execute('ALTER TABLE deeds_ledger ADD COLUMN deed_value REAL DEFAULT 0.0')
            conn.commit()
            print("✅ Added deed_value column successfully")
        else:
            print("✅ deed_value column already exists")
        
        # Verify the column was added
        cursor.execute('PRAGMA table_info(deeds_ledger)')
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        
        if 'deed_value' in new_column_names:
            print("✅ deed_value column verified")
        else:
            print("❌ deed_value column not found after adding")
            conn.close()
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing deeds_ledger table: {e}")
        return False

def create_token_classes_table():
    """Create the missing token_classes table"""
    
    print("\n🔧 Creating token_classes table...")
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='token_classes'")
        if cursor.fetchone():
            print("ℹ️ token_classes table already exists")
            conn.close()
            return True
        
        # Create token_classes table
        cursor.execute('''
            CREATE TABLE token_classes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                token_id TEXT NOT NULL,
                class_type TEXT NOT NULL,
                class_metadata TEXT DEFAULT '{}',
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                FOREIGN KEY (token_id) REFERENCES tokens(token_id),
                UNIQUE(token_id, class_type)
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX idx_token_classes_token_id ON token_classes(token_id)')
        cursor.execute('CREATE INDEX idx_token_classes_type ON token_classes(class_type)')
        
        conn.commit()
        print("✅ Created token_classes table successfully")
        
        # Verify table was created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='token_classes'")
        if cursor.fetchone():
            print("✅ token_classes table verified")
        else:
            print("❌ token_classes table not found after creation")
            conn.close()
            return False
        
        # Check table structure
        cursor.execute('PRAGMA table_info(token_classes)')
        columns = cursor.fetchall()
        print(f"✅ Table has {len(columns)} columns:")
        for col in columns:
            print(f"   - {col[1]} {col[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating token_classes table: {e}")
        return False

def populate_initial_token_classes():
    """Populate initial token classes for existing tokens"""
    
    print("\n🔧 Populating initial token classes...")
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get existing tokens
        cursor.execute("SELECT token_id, name, symbol, category FROM tokens")
        tokens = cursor.fetchall()
        
        if not tokens:
            print("ℹ️ No tokens found to create classes for")
            conn.close()
            return True
        
        print(f"Found {len(tokens)} tokens to create classes for")
        
        import time
        current_time = int(time.time())
        
        # Create basic token classes for each token
        for token in tokens:
            token_id, name, symbol, category = token
            
            # Create a basic token class
            class_type = category or 'utility'
            class_metadata = {
                'name': name,
                'symbol': symbol,
                'category': category,
                'description': f'{name} ({symbol}) token',
                'properties': {
                    'transferable': True,
                    'mintable': True,
                    'burnable': True
                }
            }
            
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO token_classes 
                    (token_id, class_type, class_metadata, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    token_id,
                    class_type,
                    str(class_metadata).replace("'", '"'),  # Convert to JSON-like string
                    current_time,
                    current_time
                ))
                
                print(f"   ✅ Created class for {name} ({symbol})")
                
            except Exception as e:
                print(f"   ⚠️ Could not create class for {name}: {e}")
        
        conn.commit()
        
        # Verify classes were created
        cursor.execute("SELECT COUNT(*) FROM token_classes")
        count = cursor.fetchone()[0]
        print(f"✅ Created {count} token classes")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error populating token classes: {e}")
        return False

def test_tokenomics_queries():
    """Test the queries that were failing"""
    
    print("\n🧪 Testing tokenomics dashboard queries...")
    
    try:
        # Connect to database
        db_path = 'shared/db/db/onnyx.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        test_identity_id = 'ONX4d0fb6bb0dda45a7'
        
        # Test deed_value query
        print("Testing deed_value query...")
        cursor.execute('''
            SELECT SUM(deed_value) as total_score
            FROM deeds_ledger
            WHERE identity_id = ?
        ''', (test_identity_id,))
        result = cursor.fetchone()
        print(f"   ✅ Deed score query successful: {result[0] if result else 0}")
        
        # Test token_classes query
        print("Testing token_classes query...")
        cursor.execute('''
            SELECT DISTINCT tc.token_id, tc.class_type, tc.class_metadata, t.name, t.symbol
            FROM token_classes tc
            JOIN tokens t ON tc.token_id = t.token_id
            JOIN token_balances tb ON t.token_id = tb.token_id
            WHERE tb.identity_id = ? AND tb.balance > 0
        ''', (test_identity_id,))
        results = cursor.fetchall()
        print(f"   ✅ Token classes query successful: {len(results)} results")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing queries: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing Biblical Tokenomics Dashboard Errors")
    print("=" * 60)
    
    # Fix deeds_ledger table
    deeds_fixed = fix_deeds_ledger_table()
    
    # Create token_classes table
    token_classes_created = create_token_classes_table()
    
    # Populate initial token classes
    classes_populated = populate_initial_token_classes()
    
    # Test the queries
    queries_ok = test_tokenomics_queries()
    
    print("\n📊 Results:")
    print("=" * 20)
    
    if deeds_fixed:
        print("✅ Deeds ledger fix: SUCCESS")
    else:
        print("❌ Deeds ledger fix: FAILED")
    
    if token_classes_created:
        print("✅ Token classes table: SUCCESS")
    else:
        print("❌ Token classes table: FAILED")
    
    if classes_populated:
        print("✅ Token classes population: SUCCESS")
    else:
        print("❌ Token classes population: FAILED")
    
    if queries_ok:
        print("✅ Query testing: SUCCESS")
    else:
        print("❌ Query testing: FAILED")
    
    overall_success = deeds_fixed and token_classes_created and classes_populated and queries_ok
    
    if overall_success:
        print("\n🎉 SUCCESS: Biblical tokenomics dashboard errors are fixed!")
        print("   - deed_value column added to deeds_ledger")
        print("   - token_classes table created with proper structure")
        print("   - Initial token classes populated")
        print("   - All queries now work without errors")
        print("   - Biblical tokenomics dashboard should now load properly")
    else:
        print("\n⚠️ Some issues remain - check details above")
    
    sys.exit(0 if overall_success else 1)
